print(f"\nInitializing ETL pipeline...")

import pandas as pd
import gzip
import os
import shutil
import connectorx as cx
from sqlalchemy import create_engine, text as sa_text, inspect
from bs4 import BeautifulSoup
import re
import warnings
from datetime import datetime
import concurrent.futures
from sentence_transformers import SentenceTransformer
import time
import subprocess
from sqlalchemy.exc import ProgrammingError
import gc
import psutil

warnings.simplefilter(action="ignore", category=FutureWarning)
pd.set_option("display.float_format", "{:.0f}".format)




# Configuration for source DB
srv_name = "HQDCPRUARMSDBRP"
db_name = "NicheRMSReport"
usr_name = "PARead"
pw = "PARead"

# Configuration for destination DB
srv_name2 = "HQDCSMOSQL01"
db_name2 = "PA_DEV2"
usr_name2 = "PAadmin"
pw2 = "PAadmin"

# Table names
src_table = "dbo.full_test"
dest_table = "ReportNLP"
staging_table = dest_table + "_staging"
logging_table = dest_table + "_log"

# === CONFIGURATION SWITCHES ===
ENABLE_FILE_OUTPUT = False  # Set to True to output files, default is False
MAX_OUTPUT_FILES = 50      # Max number of files to output if enabled
BATCH_SIZE = 300         # Reduce from 1000 to manage memory better for large datasets
NUM_WORKERS = 8           # Reduce from 12 to prevent resource contention
EMBEDDING_BATCH_SIZE = 16 # Reduce from 32 for better memory management
EMBEDDING_DEVICE = 'cpu'  # 'cpu' or 'cuda' for embedding model

# SQL Query configuration
MAIN_ETL_TOP_CLAUSE = "top 200"  # For main ETL - can be changed to "DISTINCT", "top 5000", etc.
REPROC_TOP_CLAUSE = ""           # For reprocessing - no limit (empty string)

# Additional configuration for large datasets
MAX_RETRIES = 10          # Increase retries for large dataset
RETRY_BACKOFF = 10        # Longer backoff for stability

# Thread pool executors - create once to prevent resource leaks
main_executor = None
embedding_executor = None



# Connection URL for source DB
connection_url = (
    "mssql+pyodbc://"
    f"{usr_name}:{pw}@{srv_name}/{db_name}"
    "?driver=ODBC+Driver+17+for+SQL+Server"
)
# Connection URL for destination DB
connection_url2 = (
    "mssql+pyodbc://"
    f"{usr_name2}:{pw2}@{srv_name2}/{db_name2}"
    "?driver=ODBC+Driver+17+for+SQL+Server"
)


print(f"Checking destination table {dest_table} on database {db_name2}...")
# SQL Query to load data
def dest_table_exists():
    engine = create_engine(connection_url2)
    try:
        insp = inspect(engine)
        # Remove schema if present for has_table
        table_name = dest_table.split('.')[-1]
        return insp.has_table(table_name)
    finally:
        engine.dispose()

# Step 1: Check if destination table exists and get latest Entered_Time
if dest_table_exists():
    # Use ConnectorX to get the latest entered time
    df_latest = cx.read_sql(connection_url2, f"SELECT MAX(Entered_Time) as max_time FROM {dest_table}", return_type="pandas")
    latest_entered_time = df_latest['max_time'][0] if not df_latest.empty else None

    # Get the number of rows in the destination table
    df_count = cx.read_sql(connection_url2, f"SELECT COUNT(*) as row_count FROM {dest_table}", return_type="pandas")
    dest_row_count = df_count['row_count'][0] if not df_count.empty else 0

    # Remove decimal if present
    latest_entered_time = str(latest_entered_time).split('.')[0]
    print(f"Destination table ({dest_row_count} rows) found, executing incremental load.")
    print(f"Latest record entered time in destination table: {latest_entered_time}\n")
    time_filter = f"AND TR.GOccReport_EnteredTime >= '{latest_entered_time}'"
else:
    latest_entered_time = None
    time_filter = ""
    print(f"Destination table is empty, executing full load.\n")

# Create main ETL query with configurable TOP limit using f-string
sql_query1 = f"""
DROP TABLE IF EXISTS {src_table};
WITH cte1 AS (
SELECT 
    {MAIN_ETL_TOP_CLAUSE}
    CAST(BD.Id AS VARCHAR(30)) as Id
    , BD.Data
    , BD.Type
    , CASE
        when LEFT(BD.HostId,8) = '91423001' then 'General'
        when LEFT(BD.HostId,8) = '91623001' then 'Supplementary'
        when LEFT(BD.HostId,8) = '94023001' then 'Notes'
        else Null
      END AS Report_Type
    , CAST(TR.Id AS VARCHAR(30)) AS Niche_Report_ID
    , TR.GOccReport_EnteredTime AS Entered_Time
    , TR.GOccReport_ReportTime AS Report_Time
    , TR.Remarks
    , CAST(TR.GOccReportAuth_RId AS VARCHAR(30)) AS Niche_Author_ID
    , CAST(TR.GOccReportEnter_RId AS VARCHAR(30)) AS Niche_Enter_ID
    , CAST(OC.Id AS VARCHAR(30)) AS Niche_Occurrence_ID
    , CAST(OC.OccurrenceFileNo AS VARCHAR(30)) AS Occurrence_Number
    --, OC.Occurrence_DispatchOccType AS DispatchOccType
    , SOT.DispatchOccType AS Occurrence_Type
    , OC.ESAreaLevel1 AS Zone
    , OC.ESAreaLevel3 AS Team
    , OC.ESAreaLevel5 AS Municipality
    , OC.AccessControlList
FROM TBL_BlobData							BD
    JOIN TBL_GOccurrenceTREvent				TR ON BD.HostId = TR.Id
    JOIN TBL_GOccurrence					OC ON TR.WId = OC.Id
    LEFT JOIN TBL_StandardOccurrenceType	SOT ON OC.OccurrenceStdOccType_rId = SOT.Id
WHERE OC.AccessControlList IS NULL --Occurrence is Not ACL
    AND TR.AccessControlList IS NULL --Report is NOT ACL
    AND (LEFT(BD.HostId,8) = '91423001' --General report
        OR LEFT(BD.HostId,8) = '91623001' --Supp report
        OR LEFT(BD.HostId,8) = '94023001' --Supp report
        )
        {time_filter}
),
cte2 AS (
    SELECT  
        cte1.*,
        CASE 
            WHEN PATINDEX('%[^a-zA-Z0-9]%', Type) = 0 THEN UPPER(Type)
            WHEN CHARINDEX('.', Type) > 0 
                THEN UPPER(RIGHT(Type, CHARINDEX('.', REVERSE(Type)) - 1))
            WHEN type LIKE '%unknown%' THEN 'UNKNOWN'
            ELSE UPPER(Type)
        END AS fixed_type
    from cte1
),
cte3 as(
SELECT cte2.*, 
       IIF(fixed_type LIKE '%;lz%', LEFT(type, CHARINDEX(';lz', type) - 1), fixed_type) AS real_type
FROM cte2
),
cte4 as (
select
cte3.*,
case
when real_type like 'doc%' then 'ms_word'
when real_type like 'xls%' then 'ms_excel'
when real_type in ('exe','dll','nbx','nxc','ddx','sig','xslt','xaml','unknown') then 'other'
when real_type in ('bmp','gif','jpg','jpeg','jfif','png','tif') then 'image'
when real_type in ('pdf') then 'pdf'
when real_type in ('mpg','mpeg','mp4','mkv') then 'video'
when real_type in ('mp3','wav') then 'audio'
when real_type in ('xml','json','yaml','html','htm') then 'markup'
when real_type in ('nrt','nxdx') then 'niche_markup'
when real_type in ('txt') then 'text'
else null
end as category,
IIF(fixed_type LIKE '%;lz%', '1', 0) AS 'gzip',
--GETDATE() AS proc_time,
ROW_NUMBER() OVER (PARTITION BY fixed_type ORDER BY Entered_Time desc) AS rn
from cte3
)
SELECT *
into {src_table}
FROM cte4;
--WHERE rn between 300 and 800
--and real_type != 'HTM'
--order by Entered_Time desc


-- Ensure Id is NOT NULL
ALTER TABLE {src_table} ALTER COLUMN Id VARCHAR(30) NOT NULL;
"""



sql_query2 = f"""
select *
from {src_table}
ORDER BY Entered_Time
"""

# # viewing df for debugging
# global df


def ensure_log_table():
    engine = create_engine(connection_url2)
    try:
        with engine.begin() as conn:
            conn.execute(sa_text(f'''
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{logging_table}')
                CREATE TABLE {logging_table} (
                    run INT NOT NULL,
                    id INT IDENTITY PRIMARY KEY,
                    run_time DATETIME,
                    status NVARCHAR(50),
                    batch_offset INT,
                    record_count INT,
                    source_table NVARCHAR(256),
                    dest_table NVARCHAR(256),
                    error NVARCHAR(MAX),
                    details NVARCHAR(MAX)
                )
            '''))
    finally:
        engine.dispose()

def get_next_run_number():
    """Get the next run number for this ETL execution"""
    engine = create_engine(connection_url2)
    try:
        with engine.begin() as conn:
            result = conn.execute(sa_text(f'''
                SELECT ISNULL(MAX(run), 0) + 1 as next_run FROM {logging_table}
            '''))
            return result.fetchone()[0]
    finally:
        engine.dispose()

def log_etl_run(status, batch_offset, record_count, error, details):
    engine = create_engine(connection_url2)
    try:
        with engine.begin() as conn:
            conn.execute(sa_text(f'''
                INSERT INTO {logging_table} (run, run_time, status, batch_offset, record_count, source_table, dest_table, error, details)
                VALUES (:run, GETDATE(), :status, :batch_offset, :record_count, :source_table, :dest_table, :error, :details)
            '''), {
                'run': current_run_number,
                'status': status,
                'batch_offset': batch_offset,
                'record_count': record_count,
                'source_table': f"[{srv_name}].[{db_name}].dbo.[{src_table.split('.')[-1]}]",
                'dest_table': f"[{srv_name2}].[{db_name2}].dbo.[{dest_table}]",
                'error': error,
                'details': details
            })
    finally:
        engine.dispose()

def log_memory_usage(stage):
    """Log current memory usage for monitoring"""
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"Memory usage at {stage}: {memory_mb:.1f} MB")

def force_garbage_collection():
    """Force garbage collection and log memory freed"""
    before = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
    gc.collect()
    after = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
    freed = before - after
    if freed > 10:  # Only log if significant memory was freed
        print(f"Garbage collection freed {freed:.1f} MB")

def log_connection_pool_status(engine, batch_number):
    """Log connection pool status for monitoring"""
    pool = engine.pool
    print(f"Batch {batch_number} - Pool status: "
          f"Size: {pool.size()}, "
          f"Checked in: {pool.checkedin()}, "
          f"Checked out: {pool.checkedout()}")

def cleanup_temp_directories():
    """Clean up all temporary directories"""
    temp_dirs = ["__tmp__", "rebuilt_files", "extracted_text_files"]
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"\nCleaned up temp folder.")
            except Exception as e:
                print(f"Warning: Could not clean {temp_dir}: {e}")


# Ensure log table exists before ETL
ensure_log_table()

# Get the run number for this ETL execution
current_run_number = get_next_run_number()
print(f"Starting ETL run #{current_run_number}")

# Ensure index on src_table before running ConnectorX queries
def ensure_src_table_indexes():
    engine = create_engine(connection_url)
    try:
        with engine.begin() as conn:
            # Create index on Entered_Time if not exists
            try:
                conn.execute(sa_text(f'''
                    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_full_test_EnteredTime')
                        CREATE NONCLUSTERED INDEX idx_full_test_EnteredTime ON {src_table}(Entered_Time);
                '''))
            except ProgrammingError as e:
                print(f"Index creation error (may already exist): {e}")
            # Create primary key on Id if not exists
            try:
                conn.execute(sa_text(f'''
                    IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'PK_full_test_Id')
                        ALTER TABLE {src_table} ADD CONSTRAINT PK_full_test_Id PRIMARY KEY (Id);
                '''))
            except ProgrammingError as e:
                print(f"Primary key creation error (may already exist): {e}")
    finally:
        engine.dispose()

def run_sqlcmd(sql_text, server, database, username, password):
    """
    Execute SQL code via sqlcmd using a temp file in __tmp__ folder.
    Returns stdout, raises RuntimeError on failure.
    """
    sql_temp_dir = "__tmp__"
    if not os.path.exists(sql_temp_dir):
        os.makedirs(sql_temp_dir)
    sql_file_path = os.path.join(sql_temp_dir, "temp_etl_query.sql")
    with open(sql_file_path, "w", encoding="utf-8") as f:
        f.write(sql_text)

    sqlcmd_command = [
        "sqlcmd",
        "-S", server,
        "-d", database,
        "-U", username,
        "-P", password,
        "-i", sql_file_path
    ]
    print(f"Executing SQL via sqlcmd on {server}.{database}...")
    result = subprocess.run(sqlcmd_command, capture_output=True, text=True)
    # Remove temp SQL file after execution
    if os.path.exists(sql_file_path):
        os.remove(sql_file_path)
    if result.returncode != 0:
        print("sqlcmd error:", result.stderr)
        raise RuntimeError("sqlcmd execution failed")
    else:
        print("sqlcmd output:", result.stdout)
        return result.stdout

try:
    # Create connection
    print("Connecting to source database...")
    print("Preparing source data...")
    
    # Add ETL start time tracking
    etl_start_time = datetime.now()
    print(f"ETL started at: {etl_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Log ETL start
    log_etl_run('START', 0, 0, None, f'ETL run #{current_run_number} started')
    
    # Create global temp directory once to prevent race conditions
    temp_dir_global = "__tmp__"
    os.makedirs(temp_dir_global, exist_ok=True)
    
    # Step 1: Execute sql_query1 via sqlcmd using reusable function
    run_sqlcmd(sql_query1, srv_name, db_name, usr_name, pw)
    # Wait for the table to exist before proceeding
    max_wait = 99999999  # seconds
    waited = 0
    poll_interval = 10  # seconds
    printed_wait = False
    while waited < max_wait:
        try:
            # Try a simple select to see if the table is ready
            cx.read_sql(connection_url, f"SELECT COUNT(*) as cnt FROM {src_table}", return_type="pandas")
            break
        except Exception as e:
            if not printed_wait:
                print(f"Creating table...")
                printed_wait = True
            time.sleep(poll_interval)
            waited += poll_interval
    else:
        raise RuntimeError(f"Timeout: {src_table} was not created after {max_wait} seconds.")

    # Ensure indexes exist before ConnectorX queries
    ensure_src_table_indexes()

    # Get total number of records
    total_records = cx.read_sql(connection_url, f"SELECT COUNT(*) as cnt FROM {src_table}", return_type="pandas").iloc[0, 0]
    print(f"Total records to process: {total_records}\n")
    
    # Create upload connection with better pool management
    upload_conn = create_engine(
        connection_url2, 
        connect_args={'fast_executemany': True},
        pool_size=3,          # Smaller pool
        max_overflow=0,       # No overflow
        pool_recycle=1800,    # Recycle every 30 minutes
        pool_pre_ping=True,   # Validate connections
        echo=False
    )
    
    # Create thread pools once to prevent resource leaks
    main_executor = concurrent.futures.ThreadPoolExecutor(max_workers=NUM_WORKERS)
    embedding_executor = concurrent.futures.ThreadPoolExecutor(max_workers=NUM_WORKERS)
    
    # Process in batches
    offset = 0
    batch_number = 1
    file_output_count = 0
    model = None  # Create only one model instance and reload it, which saves RAM
    MAX_RETRIES = 5
    RETRY_BACKOFF = 5  # seconds, will be multiplied by attempt number
    first_batch = True
    while offset < total_records:
        batch_start_time = datetime.now()
        print(f"Processing batch {batch_number}")
        batch_query = sql_query2 + f" OFFSET {offset} ROWS FETCH NEXT {BATCH_SIZE} ROWS ONLY"
        attempt = 0
        while attempt < MAX_RETRIES:
            try:
                df = cx.read_sql(connection_url, batch_query, return_type="pandas")
                break  # Success, exit retry loop
            except Exception as e:
                if "deadlocked" in str(e).lower():
                    attempt += 1
                    wait_time = RETRY_BACKOFF * attempt
                    print(f"Deadlock detected. Retry {attempt}/{MAX_RETRIES} after {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise  # Not a deadlock, re-raise
        else:
            print(f"Failed to execute batch_query after {MAX_RETRIES} retries due to deadlocks.")
            break  # Or continue, or raise, depending on your needs

        if df.empty:
            break

        # Create output directory for rebuilt files, and extracted text
        rebuilt_dir = "rebuilt_files"
        extracted_text_dir = "extracted_text_files"
        if ENABLE_FILE_OUTPUT:
            if not os.path.exists(rebuilt_dir):
                os.makedirs(rebuilt_dir)
            if not os.path.exists(extracted_text_dir):
                os.makedirs(extracted_text_dir)
        
        extracted_texts = [None] * len(df)
        empty_reports = [None] * len(df)
        temp_files = [None] * len(df)
        def process_row(args):
            index, row = args
            extracted_text = ""
            empty_report = 2
            temp_filename = None
            output_filename = None
            created_files = []  # Track all created files for cleanup
            
            try:
                # Use pre-created temp directory to prevent race conditions
                if ENABLE_FILE_OUTPUT and file_output_count < MAX_OUTPUT_FILES:
                    temp_dir = rebuilt_dir
                else:
                    temp_dir = temp_dir_global  # Use pre-created global temp dir
                
                # Create unique temp filename with process and thread ID to prevent conflicts
                import threading
                temp_filename = os.path.join(temp_dir, f"temp_{offset+index}_{os.getpid()}_{threading.current_thread().ident}")
                created_files.append(temp_filename)
                
                with open(temp_filename, "wb") as f:
                    f.write(row["Data"])
                
                # Set output directory and file names based on ENABLE_FILE_OUTPUT
                if ENABLE_FILE_OUTPUT and file_output_count < MAX_OUTPUT_FILES:
                    out_dir = rebuilt_dir
                else:
                    out_dir = temp_dir
                    
                if row["gzip"] == 1:
                    gz_filename = os.path.join(out_dir, f"file_{offset+index}_{os.getpid()}_{threading.current_thread().ident}.gz")
                    created_files.append(gz_filename)
                    os.rename(temp_filename, gz_filename)
                    output_filename = os.path.join(
                        out_dir,
                        f'raw_file_ind{offset+index}_{df.Id[index]}.{row["real_type"]}',
                    )
                    created_files.append(output_filename)
                    with gzip.open(gz_filename, "rb") as gz_file:
                        with open(output_filename, "wb") as output_file:
                            shutil.copyfileobj(gz_file, output_file)
                    # gz_filename will be cleaned up in finally block
                else:
                    output_filename = os.path.join(
                        out_dir,
                        f'raw_file_ind{offset+index}_{df.Id[index]}.{row["real_type"]}',
                    )
                    created_files.append(output_filename)
                    os.rename(temp_filename, output_filename)
                    
                if ENABLE_FILE_OUTPUT:
                    print(f"Successfully rebuilt file: {output_filename}")
                # Extract text for .htm files, keep only text content (no markup)
                if str(row["real_type"]).lower() == "htm":
                    try:
                        with open(
                            output_filename, "r", encoding="utf-8", errors="ignore"
                        ) as html_file:
                            soup = BeautifulSoup(html_file, "lxml")
                            for tag in soup(["script", "style"]):
                                tag.decompose()
                            extracted_text = soup.get_text(separator="\n", strip=True)
                            extracted_text = "\n".join(
                                [
                                    line
                                    for line in extracted_text.splitlines()
                                    if line.strip() != "Untitled"
                                ]
                            )
                        if extracted_text.strip() != "":
                            empty_report = 0
                        else:
                            empty_report = 1
                        # Normalize whitespace: replace runs of whitespace with single space, collapse multiple empty lines
                        extracted_text = re.sub(r"[ \t]+", " ", extracted_text)
                        extracted_text = re.sub(r"(\n\s*){2,}", "\n", extracted_text)
                        extracted_text = extracted_text.strip()
                    except Exception as e:
                        extracted_text = f"[HTML extraction error: {e}]"
                        empty_report = 2
                elif str(row["real_type"]).lower() == "nrt":
                    try:
                        with open(
                            output_filename, "r", encoding="utf-8", errors="ignore"
                        ) as nrt_file:
                            nrt_content = nrt_file.read()
                            def nrt_cleanup(text):
                                keywords = [
                                    "{ntf1",
                                    "{lnk",
                                    "{nav",
                                    "{p1l",
                                    "{pil",
                                    "{p2l",
                                    "{pal",
                                    "{pac",
                                    "{tc",
                                    "{wt",
                                    "{tr",
                                    "{rt",
                                    "{b",
                                    "{t",
                                    "{c",
                                    "{i",
                                    "{u",
                                    "}",
                                ]
                                for keyword in keywords:
                                    if keyword in text:
                                        text = text.replace(keyword, "\n")

                                # Remove all empty lines
                                filtered_text = "\n".join(
                                    line for line in text.splitlines() if line.strip()
                                )

                                # Remove only leading whitespace (spaces, tabs) from each line
                                filtered_text = "\n".join(
                                    line.lstrip() for line in filtered_text.splitlines()
                                )
                                filtered_text = "\n".join(
                                    line.rstrip() for line in filtered_text.splitlines()
                                )

                                # List of substrings that indicate a line should be removed
                                substrings_to_remove = [
                                    "+ ",
                                    "nicherms:",
                                    "Additional Templates (if applicable)",
                                    "Type narrative here",
                                    "Select the appropriate address type",
                                    "Type the occurrence address for the incident.",
                                    "CLICK HERE and either scroll",
                                    "Yes/No",
                                    "Type UCR here",
                                    "Select appropriate clearance",
                                    "Please place any CCTV in",
                                    "If responding officers are unable to",
                                ]

                                # Remove lines that start with any of the substrings
                                filtered_text = "\n".join(
                                    line
                                    for line in filtered_text.splitlines()
                                    if not any(
                                        line.startswith(sub) for sub in substrings_to_remove
                                    )
                                )

                                # List of meta
                                meta1 = [
                                    "Occurrence Date and Time",
                                    "Occurrence Address",
                                    "Person Details",
                                    "Vehicle Details",
                                    "Narrative",
                                    "UCR",
                                    "Additional Templates (if applicable)",
                                    "General Information",
                                    "Contents Inside the Vehicle",
                                    "Scene",
                                    "Canvass",
                                    "Records Information",
                                    "Occurrence Information",
                                ]
                                meta2 = [
                                    "Start Date/Time",
                                    "End Date/Time (optional)",
                                    "Date/time",
                                    "Address",
                                    "Address Type",
                                    "Involvement",
                                    "Name",
                                    "DOB",
                                    "Gender",
                                    "Race",
                                    "Phone #",
                                    "Email, Social Media, etc.",
                                    "Additional Information",
                                    "Make",
                                    "Model",
                                    "Year",
                                    "Colour",
                                    "Plate",
                                    "Province/State",
                                    "VIN",
                                    "Registered Owner",
                                    "Vehicle Driver",
                                    "Additional Information",
                                    "Location type:",
                                    "Address Common Name:",
                                    "Time theft occurred:",
                                    "Insurance on vehicle:",
                                    "Key start or push start:",
                                    "Both sets of keys/fobs:",
                                    "Value:",
                                    "Factory GPS:",
                                    "Aftermarket GPS:",
                                    "Vehicle manufacturer contacted:",
                                    "407 Transponder:",
                                    "Previous damage:",
                                    "Aftermarket add-on/Unique detailing:",
                                    "Statement obtained from Complainant:",
                                    "Property:",
                                    "Value of property:",
                                    "Last known location:",
                                    "Time last observed:",
                                    "Time when observed stolen:",
                                    "Evidence at scene:",
                                    "Doors locked:",
                                    "Ownership in vehicle:",
                                    "Insurance in vehicle:",
                                    "CCTV Available/Was it obtained:",
                                    "File location:",
                                    "Follow-up canvass:",
                                    "Expert exam required:",
                                    "Why is expert exam required:",
                                    "Special exam instructions (if required):",
                                    "Officer notifying Records:",
                                    "Time of Records notification:",
                                    "Records Information Clerk:",
                                    "If no, reason:",
                                    "MEMBER WHO LODGED PROPERTY:",
                                    "UCR:",
                                ]

                                # Remove meta code
                                def remove_meta_code(text, meta):
                                    lines = text.splitlines()
                                    result = []
                                    i = 0
                                    while i < len(lines):
                                        line = lines[i]
                                        result.append(line)
                                        if line.strip() in meta:
                                            # Check next line exists and is a 3-digit number
                                            if i + 1 < len(lines) and re.fullmatch(
                                                r"\d{3}", lines[i + 1].strip()
                                            ):
                                                i += 1 # skip the next line
                                        i += 1
                                    return "\n".join(result)

                                titles = [
                                    "GENERAL OCCURRENCE REPORT",
                                    "Supplementary Report",
                                ]
                                filtered_text = remove_meta_code(
                                    filtered_text, meta1 + meta2 + titles
                                )

                                # Remove empty meta
                                def remove_empty_meta(text, meta1, meta2):
                                    def single_pass(text, meta1, meta2):
                                        lines = text.splitlines()
                                        if not lines:
                                            return text, False

                                        result = []
                                        i = 0
                                        changes_made = False

                                        while i < len(lines):
                                            current_line = lines[i].strip()

                                            # Check if current line is in meta1 or meta2
                                            current_in_meta1 = current_line in meta1
                                            current_in_meta2 = current_line in meta2

                                            # Check next line if it exists
                                            if i + 1 < len(lines):
                                                next_line = lines[i + 1].strip()
                                                next_in_meta1 = next_line in meta1
                                                next_in_meta2 = next_line in meta2

                                                # If both current and next are in meta1, skip current
                                                if current_in_meta1 and next_in_meta1:
                                                    changes_made = True
                                                    i += 1
                                                    continue

                                                # If both current and next are in meta2, skip current
                                                if current_in_meta2 and next_in_meta2:
                                                    changes_made = True
                                                    i += 1
                                                    continue

                                                # If current is in meta2 and next is in meta1, skip current
                                                if current_in_meta2 and next_in_meta1:
                                                    changes_made = True
                                                    i += 1
                                                    continue

                                            # Add the current line to result
                                            result.append(lines[i])
                                            i += 1

                                        # Remove last line if it's in meta1 or meta2
                                        if result and (
                                            result[-1].strip() in meta1
                                            or result[-1].strip() in meta2
                                        ):
                                            result.pop()
                                            changes_made = True

                                        return "\n".join(result), changes_made

                                    # Keep processing until no more changes are made
                                    processed_text = text
                                    while True:
                                        processed_text, changes_made = single_pass(
                                            processed_text, meta1, meta2
                                        )
                                        if not changes_made:
                                            break

                                    # Edge cases
                                    processed_text = processed_text.replace(
                                        "If no, reason: UCR", "UCR"
                                    ).replace("If no, reason:\nUCR", "UCR")

                                    return processed_text

                                filtered_text = remove_empty_meta(
                                    filtered_text, meta1, meta2
                                )

                                # Add leading line break before meta1 lines and format meta2 lines
                                def format_meta_lines(text, meta1, meta2):
                                    lines = text.splitlines()
                                    result = []
                                    i = 0

                                    while i < len(lines):
                                        line = lines[i]
                                        line_stripped = line.strip()

                                        # If current line is in meta1 and it's not the first line, add empty line before
                                        if line_stripped in meta1 and len(result) > 0:
                                            result.append("")  # Add empty line
                                            result.append(line)
                                        # If line is in meta2, add ': ' at the end and concatenate with next line
                                        elif line_stripped in meta2:
                                            if i + 1 < len(lines):
                                                if ":" not in line:
                                                    # Concatenate current line + ': ' + next line
                                                    concatenated = line + ":"
                                                    result.append(concatenated)
                                                    # i += 1  # Skip the next line since we've already processed it
                                                else:
                                                    result.append(line)
                                        else:
                                            result.append(line)

                                        i += 1

                                    return "\n".join(result)

                                filtered_text = format_meta_lines(
                                    filtered_text, meta1, meta2
                                )

                                def remove_special_char_lines(text):
                                    lines = text.splitlines()
                                    result = []

                                    for line in lines:
                                        # Keep line if it's empty or contains at least one alphanumeric character or whitespace
                                        if not line.strip() or not re.match(
                                            r"^[^a-zA-Z0-9\s]+$", line.strip()
                                        ):
                                            result.append(line)

                                    return "\n".join(result)

                                filtered_text = remove_special_char_lines(filtered_text)

                                def concatenate_colon_lines(text):
                                    lines = text.splitlines()
                                    if not lines:
                                        return text

                                    result = []
                                    i = 0

                                    while i < len(lines):
                                        current_line = lines[i]

                                        # Case 1: Current line ends with ':' and next line not ends with ':'
                                        if (
                                            current_line.rstrip().endswith((":", "?"))
                                            and i + 1 < len(lines)
                                            and not lines[i + 1]
                                            .rstrip()
                                            .endswith((":", "?"))
                                        ):
                                            next_line = lines[i + 1]
                                            concatenated = current_line + " " + next_line
                                            result.append(concatenated)
                                            i += 2  # Skip the next line since we've processed it

                                        # Case 2: Current line starts with ':'
                                        elif (
                                            current_line.lstrip().startswith(":") and result
                                        ):
                                            # Modify current line to add space after ':'
                                            modified_current = current_line.lstrip()
                                            if modified_current.startswith(":"):
                                                modified_current = (
                                                    ":" + " " + modified_current[1:]
                                                )

                                            # Concatenate with previous line
                                            previous_line = (
                                                result.pop()
                                            )  # Remove the last added line
                                            concatenated = previous_line + modified_current
                                            result.append(concatenated)
                                            i += 1

                                        else:
                                            result.append(current_line)
                                            i += 1

                                    return "\n".join(result)

                                filtered_text = concatenate_colon_lines(filtered_text)

                                # Normalize whitespace: replace runs of whitespace with single space, collapse multiple empty lines
                                filtered_text = re.sub(r"[ \t]+", " ", filtered_text)
                                filtered_text = re.sub(r"(\n\s*){2,}", "\n", filtered_text)
                                filtered_text = filtered_text.strip()

                                def empty_report_check(
                                    text, allowed_list, ignore_empty=True
                                ):
                                    allowed_set = set(allowed_list)
                                    lines = (line.strip() for line in text.split("\n"))

                                    if ignore_empty:
                                        lines = (line for line in lines if line)

                                    return int(all(line in allowed_set for line in lines))

                                if_empty = empty_report_check(
                                    filtered_text, titles + meta1 + meta2
                                )
                                return filtered_text, if_empty
                            extracted_text, empty_report = nrt_cleanup(nrt_content)
                    except Exception as e:
                        extracted_text = f"[NRT extraction error: {e}]"
                        empty_report = 2
                else:
                    extracted_text = f"[File type error: Not .HTM or .NRT]"
                    empty_report = 2
                # Only output files if enabled and under limit
                if ENABLE_FILE_OUTPUT and file_output_count < MAX_OUTPUT_FILES:
                    output_text_file = os.path.join(
                        extracted_text_dir,
                        f"extracted_text_{df.real_type[index]}_ind{offset+index}_{df.Id[index]}.txt",
                    )
                    with open(output_text_file, "w") as file:
                        file.write(extracted_text)
                return index, extracted_text, empty_report, temp_filename, output_filename
            except Exception as e:
                print(f"Error processing file at index {offset+index}: {str(e)}")
                return index, extracted_text, empty_report, temp_filename, output_filename
            finally:
                # Clean up ALL created files, not just temp_filename
                for file_path in created_files:
                    if file_path and os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                        except Exception as cleanup_error:
                            print(f"Warning: Could not remove {file_path}: {cleanup_error}")
                
                # Additional cleanup for non-output files
                if not ENABLE_FILE_OUTPUT and output_filename and os.path.exists(output_filename):
                    try:
                        os.remove(output_filename)
                    except Exception:
                        pass
        # Parallel processing of rows using existing thread pool
        results = list(main_executor.map(process_row, list(df.iterrows())))
        for res in results:
            index, extracted_text, empty_report, temp_filename, output_filename = res
            extracted_texts[index] = extracted_text
            empty_reports[index] = empty_report
            temp_files[index] = temp_filename
        # Add new column to DataFrame
        df["empty_report"] = empty_reports
        df["extracted_text"] = extracted_texts
        # === Embedding generation (batch, one model instance) ===
        if model is None:
            model = SentenceTransformer('all-MiniLM-L6-v2', device=EMBEDDING_DEVICE)
        texts_to_embed = []
        valid_indices = []
        for idx, (text, empty_report) in enumerate(zip(df['extracted_text'], df['empty_report'])):
            if empty_report in [1, 2]:
                continue
            elif isinstance(text, str) and text.strip() and not text.startswith('[') and not text.endswith('error]'):
                texts_to_embed.append(text)
                valid_indices.append(idx)
        embeddings = []
        if texts_to_embed:
            # Parallel embedding generation
            def encode_batch(batch):
                return model.encode(batch, convert_to_tensor=False, show_progress_bar=False, batch_size=EMBEDDING_BATCH_SIZE)
            # Split texts_to_embed into batches
            batches = [texts_to_embed[i:i+EMBEDDING_BATCH_SIZE] for i in range(0, len(texts_to_embed), EMBEDDING_BATCH_SIZE)]
            results = list(embedding_executor.map(encode_batch, batches))
            # Flatten the list of embeddings
            embeddings = [emb for batch in results for emb in batch]
        df['embedding_vector'] = None
        if len(embeddings) > 0:
            for i, embedding in enumerate(embeddings):
                df.at[valid_indices[i], 'embedding_vector'] = embedding.tolist()
        # Convert embeddings to string format for SQL Server
        # Directly operate on df instead of making a copy
        for idx, embedding in enumerate(df['embedding_vector']):
            if embedding is not None:
                # Convert to comma-separated string
                df.at[idx, 'embedding_vector'] = ','.join(map(str, embedding))
                df.at[idx, 'empty_embedding'] = 0
                df.at[idx, 'ETL_Proc_Time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            else:
                df.at[idx, 'embedding_vector'] = None
                df.at[idx, 'empty_embedding'] = 1
                df.at[idx, 'ETL_Proc_Time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
        # print('Connect to destination database...')
        # Upload to staging table first - more memory efficient
        try:
            if first_batch:
                # Create staging table on first batch
                df.drop(columns=['Data','rn']).to_sql(staging_table, con=upload_conn, if_exists="replace", index=False)
            else:
                # Clear and populate staging table
                with upload_conn.begin() as conn:
                    conn.execute(sa_text(f"TRUNCATE TABLE {staging_table}"))
                df.drop(columns=['Data','rn']).to_sql(staging_table, con=upload_conn, if_exists="append", index=False)
            
            with upload_conn.begin() as conn:
                # Use run_sqlcmd to create dest table, PK, indexes, and fulltext
                if first_batch and not dest_table_exists():
                    dest_table_sql = f'''
                        SELECT * INTO {dest_table} FROM {staging_table} WHERE 1=0;
                        ALTER TABLE {dest_table} ALTER COLUMN Id VARCHAR(30) NOT NULL;
                        IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'PK_{dest_table}_Id')
                            ALTER TABLE {dest_table} ADD CONSTRAINT PK_{dest_table}_Id PRIMARY KEY (Id);
                        IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_{dest_table}_EnteredTime')
                            CREATE INDEX idx_{dest_table}_EnteredTime ON {dest_table}(Entered_Time);
                        IF NOT EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ftCatalog')
                            CREATE FULLTEXT CATALOG ftCatalog AS DEFAULT;
                        CREATE FULLTEXT INDEX ON {dest_table}(extracted_text LANGUAGE 1033)
                            KEY INDEX PK_{dest_table}_Id
                            ON ftCatalog;
                    '''
                    print(f"Creating destination table {dest_table}, Primary Key and indexing...")
                    run_sqlcmd(dest_table_sql, srv_name2, db_name2, usr_name2, pw2)
                
                # Upsert/dedup based on Id
                conn.execute(sa_text(f'''
                    MERGE {dest_table} AS target
                    USING {staging_table} AS source
                    ON target.Id = source.Id
                    WHEN MATCHED THEN
                        UPDATE SET
                            {', '.join([col + ' = source.' + col for col in df.drop(columns=['Data','rn','Id']).columns])}
                    WHEN NOT MATCHED BY TARGET THEN
                        INSERT ({', '.join(df.drop(columns=['Data','rn']).columns)})
                        VALUES ({', '.join(['source.' + col for col in df.drop(columns=['Data','rn']).columns])});
                '''))

        except Exception as e:
            print(f"Database upload error in batch {batch_number}: {e}")
            # Force connection cleanup on error
            upload_conn.dispose()
            raise
        first_batch = False
        batch_end_time = datetime.now()
        batch_proc_time = (batch_end_time - batch_start_time).total_seconds()
        avg_proc_time = batch_proc_time / len(df) if len(df) > 0 else 0
        
        # Calculate total elapsed time since ETL started
        total_elapsed_time = (batch_end_time - etl_start_time).total_seconds()
        total_elapsed_hours = int(total_elapsed_time // 3600)
        total_elapsed_minutes = int((total_elapsed_time % 3600) // 60)
        total_elapsed_seconds = int(total_elapsed_time % 60)
        
        print(f'Batch upload complete. Batch proc time: {batch_proc_time:.2f} sec. Avg proc time/record: {avg_proc_time:.4f} sec.')
        # Log batch status to ETL run log table
        try:
            log_etl_run('SUCCESS', offset, len(df), None, 'Batch processed successfully')
        except Exception as e:
            log_etl_run('FAILED', offset, 0, str(e), 'Batch failed')
            raise
        # Clean up temp dirs if not outputting files (but don't remove global temp dir here)
        # The global temp_dir_global will be cleaned up at the end
        
        # Periodic connection cleanup to prevent memory leaks
        if batch_number % 20 == 0:  # Every 20 batches
            print(f"Performing connection cleanup at batch {batch_number}")
            upload_conn.dispose()
            force_garbage_collection()
            
            # Recreate engine
            upload_conn = create_engine(
                connection_url2, 
                connect_args={'fast_executemany': True},
                pool_size=3,
                max_overflow=0, 
                pool_recycle=1800,
                pool_pre_ping=True,
                echo=False
            )
        
        # Log connection pool status periodically
        if batch_number % 5 == 0:
            log_connection_pool_status(upload_conn, batch_number)
        
        # Clean up large variables and force garbage collection
        del df, extracted_texts, empty_reports, results
        force_garbage_collection()
        log_memory_usage(f"end of batch {batch_number}")
        
        # Calculate and display processed percentage with elapsed time since ETL start
        processed_records = min(offset + BATCH_SIZE, total_records)
        percentage = (processed_records / total_records) * 100
        print(f"Processed: {processed_records}/{total_records} records ({percentage:.1f}%) - Elapsed Time: {total_elapsed_hours:02d}:{total_elapsed_minutes:02d}:{total_elapsed_seconds:02d}")
        
        offset += BATCH_SIZE
        batch_number += 1

    # # Drop staging table after all batches are processed
    # try:
    #     upload_conn.execute(sa_text(f"DROP TABLE IF EXISTS {staging_table}"))
    #     print(f"Staging table {staging_table} dropped.")
    # except Exception as e:
    #     print(f"Warning: Could not drop staging table: {e}")

    # Empty Report Reprocessing Section ===
    print("Main ETL done.\n\nStarting Empty Report Reprocessing...")
    
    # Step 1: Find records with empty reports
    empty_report_query = f"SELECT Id FROM {dest_table} WHERE empty_report != 0"
    empty_report_df = cx.read_sql(connection_url2, empty_report_query, return_type="pandas")
    
    if not empty_report_df.empty:
        # Truncate src table first
        engine = create_engine(connection_url)
        try:
            with engine.begin() as conn:
                conn.execute(sa_text(f"IF OBJECT_ID('{src_table}', 'U') IS NOT NULL BEGIN TRUNCATE TABLE {src_table}; END"))
        finally:
            engine.dispose()

        empty_ids = empty_report_df['Id'].tolist()
        print(f"Found {len(empty_ids)} records with empty reports to reprocess")
        
        # Step 2: Create temp table with empty report IDs (faster than large IN clause)
        temp_id_table = src_table + "_empty_ids"
        print("Creating temporary table for empty report IDs...")
        
        # Create temp table and insert IDs in batches to avoid query size limits
        create_temp_table_sql = f"""
        CREATE TABLE {temp_id_table} (Id VARCHAR(30));
        CREATE INDEX IX_TempIds ON {temp_id_table}(Id);
        """
        
        # Insert IDs in batches to avoid SQL query size limits
        BATCH_INSERT_SIZE = 1000
        insert_batches = [empty_ids[i:i+BATCH_INSERT_SIZE] for i in range(0, len(empty_ids), BATCH_INSERT_SIZE)]
        
        for batch_num, id_batch in enumerate(insert_batches, 1):
            print(f"Inserting batch {batch_num}/{len(insert_batches)} of empty report IDs...")
            values_list = ','.join([f"('{id}')" for id in id_batch])
            insert_sql = f"INSERT INTO {temp_id_table} (Id) VALUES {values_list};"
            create_temp_table_sql += insert_sql
        
        # Step 3: Reuse sql_query1 with modifications for empty report reprocessing
        # Always insert the EXISTS clause at the start of the WHERE clause
        exists_clause = f"\nJOIN {temp_id_table} AS temp ON CAST(BD.Id AS VARCHAR(30)) = temp.Id\nWHERE "
        empty_report_sql = sql_query1.replace(MAIN_ETL_TOP_CLAUSE, REPROC_TOP_CLAUSE).replace(f"DROP TABLE IF EXISTS {src_table};", "").replace(f"SELECT *\ninto {src_table}\n",,"").replace(f"\nWHERE ", exists_clause, 1)    ##replace the 1st 'WHERE' only.
        
        # Step 4: Execute combined SQL (create temp table + main query)
        print("Regenerating source data for empty reports using temp table...")
        combined_sql = create_temp_table_sql + "\n\n" + empty_report_sql

        # #Debugging
        # print(combined_sql)
        # time.sleep(30)

        run_sqlcmd(combined_sql, srv_name, db_name, usr_name, pw)
        
        # Step 4: Reprocess using existing ETL logic
        print("Reprocessing empty reports...")
        empty_total = cx.read_sql(connection_url, f"SELECT COUNT(*) as cnt FROM {src_table}", return_type="pandas").iloc[0, 0]
        print(f"Reprocessing {empty_total} empty report records")
        
        # Reset variables for reprocessing (independent batch numbering)
        empty_offset = 0
        empty_batch_number = 1  # Independent batch numbering for empty reports
        
        # Reuse existing batch processing loop for empty reports
        while empty_offset < empty_total:
            batch_start_time = datetime.now()
            print(f"Processing batch {empty_batch_number}")  # Match main ETL format
            batch_query = sql_query2 + f" OFFSET {empty_offset} ROWS FETCH NEXT {BATCH_SIZE} ROWS ONLY"
            
            # Reuse existing retry logic
            attempt = 0
            while attempt < MAX_RETRIES:
                try:
                    df = cx.read_sql(connection_url, batch_query, return_type="pandas")
                    break
                except Exception as e:
                    if "deadlocked" in str(e).lower():
                        attempt += 1
                        wait_time = RETRY_BACKOFF * attempt
                        print(f"Deadlock detected. Retry {attempt}/{MAX_RETRIES} after {wait_time}s...")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise
            else:
                print(f"Failed to execute batch_query after {MAX_RETRIES} retries due to deadlocks.")
                break

            if df.empty:
                break

            # Initialize lists for processing results
            extracted_texts = [None] * len(df)
            empty_reports = [None] * len(df)
            temp_files = [None] * len(df)
            
            # Reuse the same process_row function and thread pool
            results = list(main_executor.map(process_row, list(df.iterrows())))
            for res in results:
                index, extracted_text, empty_report, temp_filename, output_filename = res
                extracted_texts[index] = extracted_text
                empty_reports[index] = empty_report
                temp_files[index] = temp_filename
            
            # Add columns to DataFrame
            df["empty_report"] = empty_reports
            df["extracted_text"] = extracted_texts
            
            # Reuse existing embedding generation logic
            texts_to_embed = []
            valid_indices = []
            for idx, (text, empty_report) in enumerate(zip(df['extracted_text'], df['empty_report'])):
                if empty_report in [1, 2]:
                    continue
                elif isinstance(text, str) and text.strip() and not text.startswith('[') and not text.endswith('error]'):
                    texts_to_embed.append(text)
                    valid_indices.append(idx)
            
            embeddings = []
            if texts_to_embed:
                # Reuse embedding generation logic
                def encode_batch(batch):
                    return model.encode(batch, convert_to_tensor=False, show_progress_bar=False, batch_size=EMBEDDING_BATCH_SIZE)
                batches = [texts_to_embed[i:i+EMBEDDING_BATCH_SIZE] for i in range(0, len(texts_to_embed), EMBEDDING_BATCH_SIZE)]
                results = list(embedding_executor.map(encode_batch, batches))
                embeddings = [emb for batch in results for emb in batch]
            
            # Process embeddings
            df['embedding_vector'] = None
            if len(embeddings) > 0:
                for i, embedding in enumerate(embeddings):
                    df.at[valid_indices[i], 'embedding_vector'] = embedding.tolist()
            
            # Convert embeddings to string format
            for idx, embedding in enumerate(df['embedding_vector']):
                if embedding is not None:
                    df.at[idx, 'embedding_vector'] = ','.join(map(str, embedding))
                    df.at[idx, 'empty_embedding'] = 0
                    df.at[idx, 'ETL_Proc_Time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
                else:
                    df.at[idx, 'embedding_vector'] = None
                    df.at[idx, 'empty_embedding'] = 1
                    df.at[idx, 'ETL_Proc_Time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
            
            # Upload to staging table and merge (reuse existing logic)
            try:
                with upload_conn.begin() as conn:
                    conn.execute(sa_text(f"TRUNCATE TABLE {staging_table}"))
                df.drop(columns=['Data','rn']).to_sql(staging_table, con=upload_conn, if_exists="append", index=False)
                
                with upload_conn.begin() as conn:
                    conn.execute(sa_text(f'''
                        MERGE {dest_table} AS target
                        USING {staging_table} AS source
                        ON target.Id = source.Id
                        WHEN MATCHED THEN
                            UPDATE SET
                                {', '.join([col + ' = source.' + col for col in df.drop(columns=['Data','rn','Id']).columns])}
                        WHEN NOT MATCHED BY TARGET THEN
                            INSERT ({', '.join(df.drop(columns=['Data','rn']).columns)})
                            VALUES ({', '.join(['source.' + col for col in df.drop(columns=['Data','rn']).columns])});
                    '''))
            except Exception as e:
                print(f"Database upload error in empty report reprocessing batch {empty_batch_number}: {e}")
                upload_conn.dispose()
                raise
            
            # Timing and logging
            batch_end_time = datetime.now()
            batch_proc_time = (batch_end_time - batch_start_time).total_seconds()
            avg_proc_time = batch_proc_time / len(df) if len(df) > 0 else 0
            
            print(f'Batch upload complete. Batch proc time: {batch_proc_time:.2f} sec. Avg proc time/record: {avg_proc_time:.4f} sec.')
            
            # Log batch status
            try:
                log_etl_run('SUCCESS', empty_offset, len(df), None, f'Empty report reprocessing batch {empty_batch_number} processed successfully')
            except Exception as e:
                log_etl_run('FAILED', empty_offset, 0, str(e), f'Empty report reprocessing batch {empty_batch_number} failed')
                raise
            
            # Reuse existing connection cleanup logic
            if empty_batch_number % 20 == 0:
                print(f"Performing connection cleanup at empty report batch {empty_batch_number}")
                upload_conn.dispose()
                force_garbage_collection()
                upload_conn = create_engine(
                    connection_url2, 
                    connect_args={'fast_executemany': True},
                    pool_size=3,
                    max_overflow=0, 
                    pool_recycle=1800,
                    pool_pre_ping=True,
                    echo=False
                )
            
            # Clean up and progress tracking
            del df, extracted_texts, empty_reports, results
            force_garbage_collection()
            
            processed_records = min(empty_offset + BATCH_SIZE, empty_total)
            percentage = (processed_records / empty_total) * 100
            print(f"Empty report reprocessing: {processed_records}/{empty_total} records ({percentage:.1f}%)")
            
            empty_offset += BATCH_SIZE
            empty_batch_number += 1
            
        # Drop temp id table
        # engine = create_engine(connection_url)
        # try:
        #     with engine.begin() as conn:
        #         conn.execute(sa_text(f"DROP TABLE IF EXISTS {temp_id_table};"))
        # finally:
        #     engine.dispose()

        print(f"Empty report reprocessing complete: {empty_total} records processed")
    else:
        print("No empty reports found to reprocess")

    # Log ETL completion
    log_etl_run('COMPLETE', 0, 0, None, f'ETL run #{current_run_number} completed successfully')

except Exception as e:
    log_etl_run('FAILED', offset, 0, str(e), 'ETL run failed')
    print(f"Error: {e}")
finally:
    # Clean shutdown of thread pools
    if 'main_executor' in locals() and main_executor:
        main_executor.shutdown(wait=True)
    if 'embedding_executor' in locals() and embedding_executor:
        embedding_executor.shutdown(wait=True)
    
    # Clean up SentenceTransformer model to prevent memory leaks
    if 'model' in locals() and model:
        try:
            del model
            # If using CUDA, clear GPU cache
            if EMBEDDING_DEVICE == 'cuda':
                import torch
                torch.cuda.empty_cache()
        except Exception as e:
            print(f"Warning: Could not clean model: {e}")
        
    # Dispose upload connection once at the end
    if 'upload_conn' in locals():
        upload_conn.dispose()
    cleanup_temp_directories()
    force_garbage_collection()

    # Add ETL start time tracking
    etl_end_time = datetime.now()
    print(f"ETL completed at: {etl_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nDone.\n")
