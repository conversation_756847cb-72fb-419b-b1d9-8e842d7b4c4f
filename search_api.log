2025-07-24 13:14:23,196 - backend_api - INFO - LMDB BM25 index built: 15 terms, 5 documents, avg_length=4.80
2025-07-24 13:14:23,494 - backend_api - ERROR - Failed to get postings for token 'fox': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,494 - backend_api - ERROR - Failed to get postings for token 'quick': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,496 - backend_api - ERROR - Failed to get postings for token 'lazy': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,497 - backend_api - ERROR - Failed to get postings for token 'dog': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,498 - backend_api - ERROR - Failed to get postings for token 'brown': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,499 - backend_api - ERROR - Failed to get postings for token 'sleep': int is not allowed for map key when strict_map_key=True
2025-07-24 13:14:23,500 - backend_api - INFO - Adding 2 documents to LMDB BM25 index incrementally...
2025-07-24 13:14:23,501 - backend_api - ERROR - Failed to batch update postings: int is not allowed for map key when strict_map_key=True
2025-07-24 13:15:30,445 - backend_api - INFO - LMDB BM25 index built: 15 terms, 5 documents, avg_length=4.80
2025-07-24 13:15:30,823 - backend_api - INFO - Adding 2 documents to LMDB BM25 index incrementally...
2025-07-24 13:15:30,829 - backend_api - INFO - Incremental update complete: 7 total docs, 19 terms
2025-07-24 13:15:30,879 - backend_api - INFO - Loaded metadata: 7 docs, 19 terms, avg_length=4.29
2025-07-24 13:15:30,880 - backend_api - INFO - LMDB BM25 index ready: 19 terms, 7 documents
2025-07-24 13:16:13,755 - backend_api - INFO - LMDB BM25 index built: 15 terms, 5 documents, avg_length=4.80
2025-07-24 13:16:14,125 - backend_api - INFO - Adding 2 documents to LMDB BM25 index incrementally...
2025-07-24 13:16:14,130 - backend_api - INFO - Incremental update complete: 7 total docs, 19 terms
2025-07-24 13:16:14,169 - backend_api - INFO - Loaded metadata: 7 docs, 19 terms, avg_length=4.29
2025-07-24 13:16:14,170 - backend_api - INFO - LMDB BM25 index ready: 19 terms, 7 documents
2025-07-24 13:19:35,603 - backend_api - INFO - LMDB BM25 index built: 26 terms, 5 documents, avg_length=5.60
2025-07-24 13:19:35,918 - backend_api - INFO - Loaded metadata: 5 docs, 26 terms, avg_length=5.60
2025-07-24 13:19:35,919 - backend_api - INFO - LMDB BM25 index ready: 26 terms, 5 documents
2025-07-24 13:21:38,897 - backend_api - INFO - LMDB BM25 index built: 137 terms, 50 documents, avg_length=35.30
2025-07-24 13:21:39,200 - backend_api - INFO - LMDB BM25 index built: 137 terms, 50 documents, avg_length=35.30
2025-07-24 13:21:39,659 - backend_api - INFO - LMDB BM25 index built: 107 terms, 20 documents, avg_length=35.30
2025-07-24 13:21:39,969 - backend_api - INFO - Adding 30 documents to LMDB BM25 index incrementally...
2025-07-24 13:21:39,978 - backend_api - INFO - Incremental update complete: 50 total docs, 117 terms
2025-07-24 13:23:07,274 - backend_api - INFO - LMDB BM25 index built: 2999 terms, 2000 documents, avg_length=26.65
2025-07-24 13:23:07,615 - backend_api - INFO - Adding 200 documents to LMDB BM25 index incrementally...
2025-07-24 13:23:07,683 - backend_api - INFO - Incremental update complete: 2200 total docs, 2999 terms
2025-07-24 13:23:07,722 - backend_api - INFO - Loaded metadata: 2200 docs, 2999 terms, avg_length=26.65
2025-07-24 13:23:07,723 - backend_api - INFO - LMDB BM25 index ready: 2999 terms, 2200 documents
2025-07-24 13:24:07,283 - backend_api - INFO - LMDB BM25 index built: 26 terms, 5 documents, avg_length=5.60
2025-07-24 13:24:07,608 - backend_api - INFO - Loaded metadata: 5 docs, 26 terms, avg_length=5.60
2025-07-24 13:24:07,609 - backend_api - INFO - LMDB BM25 index ready: 26 terms, 5 documents
2025-07-24 13:30:54,159 - __main__ - INFO - Starting up Search API...
2025-07-24 13:30:54,160 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-24 13:30:54,164 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-24 13:30:54,164 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-24 13:30:57,736 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-24 13:31:00,582 - __main__ - INFO - Model loaded successfully
2025-07-24 13:31:00,583 - __main__ - INFO - Initializing database connection...
2025-07-24 13:31:00,806 - __main__ - INFO - Database connection established
2025-07-24 13:31:00,806 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-24 13:31:00,807 - __main__ - INFO - Cache file missing: search_cache/embeddings.dat
2025-07-24 13:31:00,808 - __main__ - INFO - Cache files not found, generating new cache from database...
2025-07-24 13:31:00,808 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-24 13:31:01,301 - __main__ - INFO - Total records to process: 395296
2025-07-24 13:31:01,308 - __main__ - INFO - Processing chunk 1: records 0-5000
2025-07-24 13:31:07,843 - __main__ - INFO - GC: 0 objects collected, 9.9MB freed
2025-07-24 13:31:07,843 - __main__ - INFO - Progress: 5000/395296 records processed (1.3%)
2025-07-24 13:31:07,844 - __main__ - INFO - Processing chunk 2: records 5000-10000
2025-07-24 13:31:12,851 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:31:12,852 - __main__ - INFO - Processing chunk 3: records 10000-15000
2025-07-24 13:31:17,871 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:31:17,872 - __main__ - INFO - Processing chunk 4: records 15000-20000
2025-07-24 13:31:23,009 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:31:23,010 - __main__ - INFO - Processing chunk 5: records 20000-25000
2025-07-24 13:31:28,127 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:31:28,127 - __main__ - INFO - Processing chunk 6: records 25000-30000
2025-07-24 13:31:33,570 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:31:33,571 - __main__ - INFO - Progress: 30000/395296 records processed (7.6%)
2025-07-24 13:31:33,571 - __main__ - INFO - Processing chunk 7: records 30000-35000
2025-07-24 13:31:41,146 - __main__ - INFO - Processing chunk 8: records 35000-40000
2025-07-24 13:31:47,765 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:31:47,766 - __main__ - INFO - Processing chunk 9: records 40000-45000
2025-07-24 13:31:54,663 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:31:54,664 - __main__ - INFO - Processing chunk 10: records 45000-50000
2025-07-24 13:32:03,105 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:32:03,105 - __main__ - INFO - Processing chunk 11: records 50000-55000
2025-07-24 13:32:11,450 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:32:11,451 - __main__ - INFO - Progress: 55000/395296 records processed (13.9%)
2025-07-24 13:32:11,451 - __main__ - INFO - Processing chunk 12: records 55000-60000
2025-07-24 13:32:19,558 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:32:19,559 - __main__ - INFO - Processing chunk 13: records 60000-65000
2025-07-24 13:32:27,732 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:32:27,732 - __main__ - INFO - Processing chunk 14: records 65000-70000
2025-07-24 13:32:36,244 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:32:36,245 - __main__ - INFO - Processing chunk 15: records 70000-75000
2025-07-24 13:32:45,185 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:32:45,186 - __main__ - INFO - Processing chunk 16: records 75000-80000
2025-07-24 13:32:54,619 - __main__ - INFO - GC: 0 objects collected, 7.0MB freed
2025-07-24 13:32:54,619 - __main__ - INFO - Progress: 80000/395296 records processed (20.2%)
2025-07-24 13:32:54,620 - __main__ - INFO - Processing chunk 17: records 80000-85000
2025-07-24 13:33:06,063 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:33:06,063 - __main__ - INFO - Processing chunk 18: records 85000-90000
2025-07-24 13:33:16,867 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:33:16,867 - __main__ - INFO - Processing chunk 19: records 90000-95000
2025-07-24 13:33:27,313 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:33:27,314 - __main__ - INFO - Processing chunk 20: records 95000-100000
2025-07-24 13:33:37,964 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:33:37,965 - __main__ - INFO - Processing chunk 21: records 100000-105000
2025-07-24 13:33:49,635 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:33:49,636 - __main__ - INFO - Progress: 105000/395296 records processed (26.6%)
2025-07-24 13:33:49,636 - __main__ - INFO - Processing chunk 22: records 105000-110000
2025-07-24 13:34:01,585 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:34:01,586 - __main__ - INFO - Processing chunk 23: records 110000-115000
2025-07-24 13:34:15,746 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:34:15,746 - __main__ - INFO - Processing chunk 24: records 115000-120000
2025-07-24 13:34:30,386 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:34:30,387 - __main__ - INFO - Processing chunk 25: records 120000-125000
2025-07-24 13:34:45,076 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:34:45,077 - __main__ - INFO - Processing chunk 26: records 125000-130000
2025-07-24 13:34:59,998 - __main__ - INFO - GC: 0 objects collected, 7.0MB freed
2025-07-24 13:34:59,999 - __main__ - INFO - Progress: 130000/395296 records processed (32.9%)
2025-07-24 13:34:59,999 - __main__ - INFO - Processing chunk 27: records 130000-135000
2025-07-24 13:35:15,814 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:35:15,814 - __main__ - INFO - Processing chunk 28: records 135000-140000
2025-07-24 13:35:30,307 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:35:30,308 - __main__ - INFO - Processing chunk 29: records 140000-145000
2025-07-24 13:35:47,053 - __main__ - INFO - Processing chunk 30: records 145000-150000
2025-07-24 13:36:02,859 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:36:02,859 - __main__ - INFO - Processing chunk 31: records 150000-155000
2025-07-24 13:36:19,223 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:36:19,223 - __main__ - INFO - Progress: 155000/395296 records processed (39.2%)
2025-07-24 13:36:19,224 - __main__ - INFO - Processing chunk 32: records 155000-160000
2025-07-24 13:36:35,492 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:36:35,492 - __main__ - INFO - Processing chunk 33: records 160000-165000
2025-07-24 13:36:51,558 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:36:51,559 - __main__ - INFO - Processing chunk 34: records 165000-170000
2025-07-24 13:37:10,107 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:37:10,107 - __main__ - INFO - Processing chunk 35: records 170000-175000
2025-07-24 13:37:26,910 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:37:26,910 - __main__ - INFO - Processing chunk 36: records 175000-180000
2025-07-24 13:37:45,035 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:37:45,035 - __main__ - INFO - Progress: 180000/395296 records processed (45.5%)
2025-07-24 13:37:45,036 - __main__ - INFO - Processing chunk 37: records 180000-185000
2025-07-24 13:38:03,956 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:38:03,956 - __main__ - INFO - Processing chunk 38: records 185000-190000
2025-07-24 13:38:24,181 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:38:24,182 - __main__ - INFO - Processing chunk 39: records 190000-195000
2025-07-24 13:38:44,237 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:38:44,238 - __main__ - INFO - Processing chunk 40: records 195000-200000
2025-07-24 13:39:04,605 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:39:04,605 - __main__ - INFO - Processing chunk 41: records 200000-205000
2025-07-24 13:39:24,412 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:39:24,412 - __main__ - INFO - Progress: 205000/395296 records processed (51.9%)
2025-07-24 13:39:24,413 - __main__ - INFO - Processing chunk 42: records 205000-210000
2025-07-24 13:39:45,838 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:39:45,839 - __main__ - INFO - Processing chunk 43: records 210000-215000
2025-07-24 13:40:07,670 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:40:07,670 - __main__ - INFO - Processing chunk 44: records 215000-220000
2025-07-24 13:40:28,680 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:40:28,681 - __main__ - INFO - Processing chunk 45: records 220000-225000
2025-07-24 13:40:50,389 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:40:50,390 - __main__ - INFO - Processing chunk 46: records 225000-230000
2025-07-24 13:41:13,501 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:41:13,501 - __main__ - INFO - Progress: 230000/395296 records processed (58.2%)
2025-07-24 13:41:13,502 - __main__ - INFO - Processing chunk 47: records 230000-235000
2025-07-24 13:41:35,723 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:41:35,724 - __main__ - INFO - Processing chunk 48: records 235000-240000
2025-07-24 13:41:58,749 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:41:58,749 - __main__ - INFO - Processing chunk 49: records 240000-245000
2025-07-24 13:42:34,212 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:42:34,212 - __main__ - INFO - Processing chunk 50: records 245000-250000
2025-07-24 13:42:58,303 - __main__ - INFO - GC: 0 objects collected, 8.0MB freed
2025-07-24 13:42:58,304 - __main__ - INFO - Processing chunk 51: records 250000-255000
2025-07-24 13:43:25,467 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 13:43:25,468 - __main__ - INFO - Progress: 255000/395296 records processed (64.5%)
2025-07-24 13:43:25,468 - __main__ - INFO - Processing chunk 52: records 255000-260000
2025-07-24 13:43:49,879 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:43:49,879 - __main__ - INFO - Processing chunk 53: records 260000-265000
2025-07-24 13:44:17,526 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:44:17,527 - __main__ - INFO - Processing chunk 54: records 265000-270000
2025-07-24 13:44:42,942 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:44:42,943 - __main__ - INFO - Processing chunk 55: records 270000-275000
2025-07-24 13:45:10,529 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:45:10,530 - __main__ - INFO - Processing chunk 56: records 275000-280000
2025-07-24 13:45:37,681 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:45:37,682 - __main__ - INFO - Progress: 280000/395296 records processed (70.8%)
2025-07-24 13:45:37,682 - __main__ - INFO - Processing chunk 57: records 280000-285000
2025-07-24 13:46:07,586 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:46:07,586 - __main__ - INFO - Processing chunk 58: records 285000-290000
2025-07-24 13:46:35,370 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:46:35,370 - __main__ - INFO - Processing chunk 59: records 290000-295000
2025-07-24 13:47:03,106 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:47:03,106 - __main__ - INFO - Processing chunk 60: records 295000-300000
2025-07-24 13:47:33,366 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:47:33,367 - __main__ - INFO - Processing chunk 61: records 300000-305000
2025-07-24 13:48:02,228 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:48:02,228 - __main__ - INFO - Progress: 305000/395296 records processed (77.2%)
2025-07-24 13:48:02,229 - __main__ - INFO - Processing chunk 62: records 305000-310000
2025-07-24 13:48:32,863 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:48:32,863 - __main__ - INFO - Processing chunk 63: records 310000-315000
2025-07-24 13:49:02,566 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:49:02,567 - __main__ - INFO - Processing chunk 64: records 315000-320000
2025-07-24 13:49:35,178 - __main__ - INFO - GC: 0 objects collected, 13.0MB freed
2025-07-24 13:49:35,179 - __main__ - INFO - Processing chunk 65: records 320000-325000
2025-07-24 13:50:06,923 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:50:06,924 - __main__ - INFO - Processing chunk 66: records 325000-330000
2025-07-24 13:50:38,309 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:50:38,310 - __main__ - INFO - Progress: 330000/395296 records processed (83.5%)
2025-07-24 13:50:38,311 - __main__ - INFO - Processing chunk 67: records 330000-335000
2025-07-24 13:51:11,245 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:51:11,245 - __main__ - INFO - Processing chunk 68: records 335000-340000
2025-07-24 13:51:43,444 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:51:43,445 - __main__ - INFO - Processing chunk 69: records 340000-345000
2025-07-24 13:52:20,848 - __main__ - INFO - GC: 0 objects collected, 11.0MB freed
2025-07-24 13:52:20,848 - __main__ - INFO - Processing chunk 70: records 345000-350000
2025-07-24 13:52:58,620 - __main__ - INFO - GC: 0 objects collected, 12.0MB freed
2025-07-24 13:52:58,621 - __main__ - INFO - Processing chunk 71: records 350000-355000
2025-07-24 13:53:34,500 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:53:34,500 - __main__ - INFO - Progress: 355000/395296 records processed (89.8%)
2025-07-24 13:53:34,501 - __main__ - INFO - Processing chunk 72: records 355000-360000
2025-07-24 13:54:11,506 - __main__ - INFO - GC: 0 objects collected, 16.0MB freed
2025-07-24 13:54:11,506 - __main__ - INFO - Processing chunk 73: records 360000-365000
2025-07-24 13:54:47,225 - __main__ - INFO - GC: 0 objects collected, 10.0MB freed
2025-07-24 13:54:47,225 - __main__ - INFO - Processing chunk 74: records 365000-370000
2025-07-24 13:55:24,895 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:55:24,896 - __main__ - INFO - Processing chunk 75: records 370000-375000
2025-07-24 13:56:01,259 - __main__ - INFO - GC: 0 objects collected, 7.0MB freed
2025-07-24 13:56:01,259 - __main__ - INFO - Processing chunk 76: records 375000-380000
2025-07-24 13:56:41,713 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:56:41,714 - __main__ - INFO - Progress: 380000/395296 records processed (96.1%)
2025-07-24 13:56:41,714 - __main__ - INFO - Processing chunk 77: records 380000-385000
2025-07-24 13:57:31,260 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:57:31,260 - __main__ - INFO - Processing chunk 78: records 385000-390000
2025-07-24 13:58:10,538 - __main__ - INFO - GC: 0 objects collected, 14.0MB freed
2025-07-24 13:58:10,539 - __main__ - INFO - Processing chunk 79: records 390000-395000
2025-07-24 13:58:49,845 - __main__ - INFO - GC: 0 objects collected, 16.0MB freed
2025-07-24 13:58:49,845 - __main__ - INFO - Processing chunk 80: records 395000-395296
2025-07-24 13:59:23,262 - __main__ - INFO - LMDB BM25 index built: 662978 terms, 395296 documents, avg_length=130.39
2025-07-24 13:59:23,808 - __main__ - INFO - Data loading complete: 395296 records saved to search_cache
2025-07-24 13:59:23,962 - __main__ - INFO - Chunked cache loading completed in 1703.16s
2025-07-24 13:59:23,963 - __main__ - INFO - Loaded 395296 records with memory-mapped storage
2025-07-24 13:59:24,243 - __main__ - INFO - Search cache loaded: 395296 records
2025-07-24 13:59:24,244 - __main__ - INFO - Memory-mapped storage: True
2025-07-24 13:59:24,244 - __main__ - INFO - Starting system monitor...
2025-07-24 13:59:24,246 - __main__ - INFO - 
System monitor started

2025-07-24 13:59:24,246 - __main__ - INFO - Starting cache update monitor...
2025-07-24 13:59:24,247 - __main__ - INFO - Cache update monitor started
2025-07-24 13:59:24,284 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:00:24,303 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:01:24,321 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:02:07,001 - __main__ - INFO - Request 1753380127-2245468023856: GET http://localhost:8000/search?q=gun&top_k=20&semantic_weight=0.1&lexical_weight=0.9&min_similarity=0.1&table=ReportNLP
2025-07-24 14:02:07,002 - __main__ - INFO - Processing search request: 'gun' on table 'ReportNLP'
2025-07-24 14:02:07,003 - __main__ - INFO - Processing search: 'gun' (Memory: 868.0MB)
2025-07-24 14:02:08,630 - __main__ - INFO - Search completed: 20 results in 1626.84ms (Memory: 868.0MB -> 1494.2MB, diff: +626.1MB)
2025-07-24 14:02:08,798 - __main__ - WARNING - Request 1753380127-2245468023856 increased memory by 626.3MB (from 868.0MB to 1494.3MB)
2025-07-24 14:02:08,977 - __main__ - INFO - Request 1753380127-2245468023856 completed in 1798.36ms with status 200
2025-07-24 14:02:24,341 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:03:06,701 - __main__ - INFO - Request 1753380186-2245468662784: GET http://localhost:8000/search?q=red%20car&top_k=20&semantic_weight=0.1&lexical_weight=0.9&min_similarity=0.1&table=ReportNLP
2025-07-24 14:03:06,703 - __main__ - INFO - Processing search request: 'red car' on table 'ReportNLP'
2025-07-24 14:03:06,704 - __main__ - INFO - Processing search: 'red car' (Memory: 1494.5MB)
2025-07-24 14:03:08,019 - __main__ - INFO - Search completed: 20 results in 1314.96ms (Memory: 1494.5MB -> 1496.1MB, diff: +1.6MB)
2025-07-24 14:03:08,022 - __main__ - INFO - Request 1753380186-2245468662784 completed in 1320.95ms with status 200
2025-07-24 14:03:24,361 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:04:24,389 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:05:24,457 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:06:24,582 - __main__ - INFO - Starting incremental update for 600 new records
2025-07-24 14:06:24,583 - __main__ - INFO - Loading new records since 2016-07-28 12:18:06.660000 with chunk size 5000
2025-07-24 14:06:24,599 - __main__ - INFO - Loading 600 new records incrementally
2025-07-24 14:06:24,600 - __main__ - INFO - Processing incremental chunk 1: records 0-600
2025-07-24 14:06:27,156 - __main__ - INFO - Incremental progress: 600/600 records processed (100.0%)
2025-07-24 14:06:27,158 - __main__ - INFO - Incremental loading complete: 600 new records loaded
2025-07-24 14:06:27,159 - __main__ - INFO - Starting incremental cache update with 600 new records
2025-07-24 14:06:27,411 - __main__ - INFO - Extending embeddings from 395296 to 395896 records
2025-07-24 14:06:28,271 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 14:06:31,787 - __main__ - INFO - Embeddings file extended successfully to 395896 records
2025-07-24 14:06:31,841 - __main__ - INFO - Adding 600 documents to LMDB BM25 index incrementally...
2025-07-24 14:07:18,657 - __main__ - INFO - Incremental update complete: 395896 total docs, 664107 terms
2025-07-24 14:07:18,862 - __main__ - INFO - Incremental cache update completed in 51.70s
2025-07-24 14:07:18,862 - __main__ - INFO - Cache now contains 395896 total records
2025-07-24 14:07:19,159 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 600, 'total_records': 395896, 'update_time_seconds': 51.701590061187744}
2025-07-24 14:07:19,161 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 600, 'total_records': 395896, 'update_time_seconds': 51.701590061187744, 'latest_entered_time': '2016-07-28 12:18:06.660000', 'memory_used_mb': 0.87890625}
2025-07-24 14:08:19,161 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-09-02 09:52:14.370000. Check Interval: 60s
2025-07-24 14:08:19,177 - __main__ - INFO - Starting incremental update for 400 new records
2025-07-24 14:08:19,178 - __main__ - INFO - Loading new records since 2016-09-02 09:52:14.370000 with chunk size 5000
2025-07-24 14:08:19,194 - __main__ - INFO - Loading 400 new records incrementally
2025-07-24 14:08:19,195 - __main__ - INFO - Processing incremental chunk 1: records 0-400
2025-07-24 14:08:19,683 - __main__ - INFO - Incremental progress: 400/400 records processed (100.0%)
2025-07-24 14:08:19,684 - __main__ - INFO - Incremental loading complete: 400 new records loaded
2025-07-24 14:08:19,685 - __main__ - INFO - Starting incremental cache update with 400 new records
2025-07-24 14:08:19,728 - __main__ - INFO - Extending embeddings from 395896 to 396296 records
2025-07-24 14:08:19,872 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 14:08:21,211 - __main__ - INFO - Embeddings file extended successfully to 396296 records
2025-07-24 14:08:21,212 - __main__ - INFO - Adding 400 documents to LMDB BM25 index incrementally...
2025-07-24 14:08:52,586 - __main__ - INFO - Incremental update complete: 396296 total docs, 665000 terms
2025-07-24 14:08:52,796 - __main__ - INFO - Incremental cache update completed in 33.11s
2025-07-24 14:08:52,797 - __main__ - INFO - Cache now contains 396296 total records
2025-07-24 14:08:53,141 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 400, 'total_records': 396296, 'update_time_seconds': 33.111608028411865}
2025-07-24 14:08:53,143 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 400, 'total_records': 396296, 'update_time_seconds': 33.111608028411865, 'latest_entered_time': '2016-09-02 09:52:14.370000', 'memory_used_mb': 0.5859375}
2025-07-24 14:09:53,144 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:09:53,157 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:10:32,698 - __main__ - INFO - Request 1753380632-2245532719664: GET http://localhost:8000/search?q=yellow%20car&top_k=20&semantic_weight=0.1&lexical_weight=0.9&min_similarity=0.1&table=ReportNLP
2025-07-24 14:10:32,700 - __main__ - INFO - Processing search request: 'yellow car' on table 'ReportNLP'
2025-07-24 14:10:32,700 - __main__ - INFO - Processing search: 'yellow car' (Memory: 830.8MB)
2025-07-24 14:10:34,115 - __main__ - INFO - Search completed: 20 results in 1414.97ms (Memory: 830.8MB -> 1411.3MB, diff: +580.5MB)
2025-07-24 14:10:34,281 - __main__ - WARNING - Request 1753380632-2245532719664 increased memory by 580.5MB (from 830.8MB to 1411.3MB)
2025-07-24 14:10:34,468 - __main__ - INFO - Request 1753380632-2245532719664 completed in 1582.11ms with status 200
2025-07-24 14:10:53,157 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:10:53,164 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:11:53,165 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:11:53,191 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:12:53,192 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:12:53,199 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:13:53,200 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:13:53,207 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:14:06,576 - __main__ - INFO - Shutting down Search API...
2025-07-24 14:14:08,587 - __main__ - INFO - Cache update monitor stopped
2025-07-24 14:14:09,596 - __main__ - INFO - System monitor stopped
2025-07-24 14:14:09,598 - __main__ - INFO - Database connection closed
2025-07-24 14:17:54,262 - __main__ - INFO - Starting up Search API...
2025-07-24 14:17:54,262 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-24 14:17:54,265 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-24 14:17:54,266 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-24 14:17:57,228 - __main__ - INFO - Model loaded successfully
2025-07-24 14:17:57,229 - __main__ - INFO - Initializing database connection...
2025-07-24 14:17:57,338 - __main__ - INFO - Database connection established
2025-07-24 14:17:57,338 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-24 14:17:57,339 - __main__ - INFO - Cache file missing: search_cache/bm25_vocab.pkl
2025-07-24 14:17:57,340 - __main__ - INFO - Cache files not found, generating new cache from database...
2025-07-24 14:17:57,340 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-24 14:17:57,818 - __main__ - INFO - Total records to process: 396296
2025-07-24 14:17:57,941 - __main__ - INFO - Loaded metadata: 396296 docs, 665000 terms, avg_length=130.55
2025-07-24 14:17:57,942 - __main__ - INFO - Processing chunk 1: records 0-5000
2025-07-24 14:18:41,102 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 14:18:41,103 - __main__ - INFO - Progress: 5000/396296 records processed (1.3%)
2025-07-24 14:18:41,104 - __main__ - INFO - Processing chunk 2: records 5000-10000
2025-07-24 14:19:27,062 - __main__ - INFO - GC: 0 objects collected, 9.0MB freed
2025-07-24 14:19:27,063 - __main__ - INFO - Processing chunk 3: records 10000-15000
2025-07-24 14:20:03,317 - __main__ - INFO - GC: 0 objects collected, 7.0MB freed
2025-07-24 14:20:03,318 - __main__ - INFO - Processing chunk 4: records 15000-20000
2025-07-24 14:20:40,995 - __main__ - INFO - GC: 0 objects collected, 7.0MB freed
2025-07-24 14:20:40,996 - __main__ - INFO - Processing chunk 5: records 20000-25000
2025-07-24 14:30:03,348 - backend_api - INFO - Cache file missing: C:\Users\<USER>\AppData\Local\Temp\test_cache_detection_bfbjhjhg/embeddings.dat
2025-07-24 14:30:03,353 - backend_api - INFO - Cache file missing: C:\Users\<USER>\AppData\Local\Temp\test_cache_detection_bfbjhjhg/ids.pkl
2025-07-24 14:30:03,369 - backend_api - ERROR - Failed to initialize LMDB: C:\Users\<USER>\AppData\Local\Temp\test_cache_detection_bfbjhjhg\bm25.lmdb: There is not enough space on the disk.


2025-07-24 14:30:03,389 - backend_api - ERROR - Failed to initialize LMDB: C:\Users\<USER>\AppData\Local\Temp\test_cache_structure_6unwrafn\bm25.lmdb: There is not enough space on the disk.


2025-07-24 14:33:31,109 - __main__ - INFO - Starting up Search API...
2025-07-24 14:33:31,110 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-24 14:33:31,114 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-24 14:33:31,115 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-24 14:33:35,253 - __main__ - INFO - Model loaded successfully
2025-07-24 14:33:35,254 - __main__ - INFO - Initializing database connection...
2025-07-24 14:33:35,402 - __main__ - INFO - Database connection established
2025-07-24 14:33:35,402 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-24 14:33:35,404 - __main__ - INFO - All cache files found on disk. Attempting to load from disk...
2025-07-24 14:33:35,486 - __main__ - INFO - Loading IDs from search_cache/ids.pkl (396296 records)
2025-07-24 14:33:35,544 - __main__ - INFO - Loading embeddings from search_cache/embeddings.dat (580.5MB, shape=(396296, 384))
2025-07-24 14:33:35,656 - __main__ - INFO - Loaded metadata: 396296 docs, 665000 terms, avg_length=130.55
2025-07-24 14:33:35,657 - __main__ - INFO - LMDB BM25 index ready: 665000 terms, 396296 documents
2025-07-24 14:33:35,657 - __main__ - INFO - Cache loaded from disk in 0.25s
2025-07-24 14:33:35,658 - __main__ - INFO - Loaded 396296 records with memory-mapped storage
2025-07-24 14:33:36,150 - __main__ - INFO - Search cache loaded: 396296 records
2025-07-24 14:33:36,150 - __main__ - INFO - Memory-mapped storage: True
2025-07-24 14:33:36,151 - __main__ - INFO - Starting system monitor...
2025-07-24 14:33:36,152 - __main__ - INFO - 
System monitor started

2025-07-24 14:33:36,153 - __main__ - INFO - Starting cache update monitor...
2025-07-24 14:33:36,154 - __main__ - INFO - Cache update monitor started
2025-07-24 14:33:36,155 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:33:36,163 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:34:36,164 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:34:36,171 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:35:36,172 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:35:36,195 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:36:36,195 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:36:36,202 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:37:36,203 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:37:36,219 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:38:36,220 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:38:36,229 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:39:36,229 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:39:36,239 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:40:36,239 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:40:36,247 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:41:36,247 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-14 17:30:11.747000. Check Interval: 60s
2025-07-24 14:41:36,375 - __main__ - INFO - Starting incremental update for 600 new records
2025-07-24 14:41:36,459 - __main__ - INFO - Loading new records since 2016-10-14 17:30:11.747000 with chunk size 5000
2025-07-24 14:41:36,583 - __main__ - INFO - Loading 600 new records incrementally
2025-07-24 14:41:36,606 - __main__ - INFO - Processing incremental chunk 1: records 0-600
2025-07-24 14:41:40,587 - __main__ - INFO - Incremental progress: 600/600 records processed (100.0%)
2025-07-24 14:41:40,700 - __main__ - INFO - Incremental loading complete: 600 new records loaded
2025-07-24 14:41:40,857 - __main__ - INFO - Starting incremental cache update with 600 new records
2025-07-24 14:41:41,091 - __main__ - INFO - Extending embeddings from 396296 to 396896 records
2025-07-24 14:41:41,870 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 14:41:47,022 - __main__ - INFO - Embeddings file extended successfully to 396896 records
2025-07-24 14:41:47,226 - __main__ - INFO - Adding 600 documents to LMDB BM25 index incrementally...
2025-07-24 14:43:04,637 - __main__ - INFO - Incremental update complete: 396896 total docs, 665936 terms
2025-07-24 14:43:04,976 - __main__ - INFO - Incremental cache update completed in 84.12s
2025-07-24 14:43:04,977 - __main__ - INFO - Cache now contains 396896 total records
2025-07-24 14:43:05,397 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 600, 'total_records': 396896, 'update_time_seconds': 84.11908960342407}
2025-07-24 14:43:05,398 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 600, 'total_records': 396896, 'update_time_seconds': 84.11908960342407, 'latest_entered_time': '2016-10-14 17:30:11.747000', 'memory_used_mb': 0.87890625}
2025-07-24 14:44:05,400 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-10-21 21:28:50.930000. Check Interval: 60s
2025-07-24 14:44:05,416 - __main__ - INFO - Starting incremental update for 400 new records
2025-07-24 14:44:05,417 - __main__ - INFO - Loading new records since 2016-10-21 21:28:50.930000 with chunk size 5000
2025-07-24 14:44:05,431 - __main__ - INFO - Loading 400 new records incrementally
2025-07-24 14:44:05,432 - __main__ - INFO - Processing incremental chunk 1: records 0-400
2025-07-24 14:44:06,031 - __main__ - INFO - Incremental progress: 400/400 records processed (100.0%)
2025-07-24 14:44:06,032 - __main__ - INFO - Incremental loading complete: 400 new records loaded
2025-07-24 14:44:06,033 - __main__ - INFO - Starting incremental cache update with 400 new records
2025-07-24 14:44:06,082 - __main__ - INFO - Extending embeddings from 396896 to 397296 records
2025-07-24 14:44:06,223 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 14:44:07,729 - __main__ - INFO - Embeddings file extended successfully to 397296 records
2025-07-24 14:44:07,730 - __main__ - INFO - Adding 400 documents to LMDB BM25 index incrementally...
2025-07-24 14:44:55,850 - __main__ - INFO - Incremental update complete: 397296 total docs, 666727 terms
2025-07-24 14:44:56,222 - __main__ - INFO - Incremental cache update completed in 50.19s
2025-07-24 14:44:56,223 - __main__ - INFO - Cache now contains 397296 total records
2025-07-24 14:44:56,673 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 400, 'total_records': 397296, 'update_time_seconds': 50.1889214515686}
2025-07-24 14:44:56,674 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 400, 'total_records': 397296, 'update_time_seconds': 50.1889214515686, 'latest_entered_time': '2016-10-21 21:28:50.930000', 'memory_used_mb': 0.5859375}
2025-07-24 14:45:56,675 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:45:56,700 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:46:56,701 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:46:56,711 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:47:56,713 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:47:56,721 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:48:56,722 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:48:56,735 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:49:56,737 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:49:56,759 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:50:56,772 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:50:56,791 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:51:56,795 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:51:56,806 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:52:56,806 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:52:56,813 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:53:56,814 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:53:56,821 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:54:56,821 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:54:56,830 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:55:56,831 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:55:56,838 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:56:56,838 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:56:56,846 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:57:56,847 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:57:56,854 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:58:56,855 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:58:56,862 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 14:59:56,862 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 14:59:56,872 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:00:56,873 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:00:56,971 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:01:56,972 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:01:56,979 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:02:56,980 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:02:56,991 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:03:56,991 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:03:56,998 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:04:56,999 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:04:57,007 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:05:57,008 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:05:57,014 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:06:57,015 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:06:57,022 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:07:57,023 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:07:57,030 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:08:57,031 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:08:57,038 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:09:57,039 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:09:57,047 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:10:57,047 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:10:57,053 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:11:57,055 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:11:57,078 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:12:57,079 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:12:57,086 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:13:57,088 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 60s
2025-07-24 15:13:57,095 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:14:46,864 - __main__ - INFO - Shutting down Search API...
2025-07-24 15:14:48,867 - __main__ - INFO - Cache update monitor stopped
2025-07-24 15:14:49,877 - __main__ - INFO - System monitor stopped
2025-07-24 15:14:49,881 - __main__ - INFO - Database connection closed
2025-07-24 15:18:30,175 - __main__ - INFO - Starting up Search API...
2025-07-24 15:18:30,177 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-24 15:18:30,185 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-24 15:18:30,185 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-24 15:18:36,605 - __main__ - INFO - Model loaded successfully
2025-07-24 15:18:36,605 - __main__ - INFO - Initializing database connection...
2025-07-24 15:18:36,813 - __main__ - INFO - Database connection established
2025-07-24 15:18:36,814 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-24 15:18:36,815 - __main__ - INFO - All cache files found on disk. Attempting to load from disk...
2025-07-24 15:18:36,902 - __main__ - INFO - Loading IDs from search_cache/ids.pkl (397296 records)
2025-07-24 15:18:36,962 - __main__ - INFO - Loading embeddings from search_cache/embeddings.dat (582.0MB, shape=(397296, 384))
2025-07-24 15:18:37,109 - __main__ - INFO - Loaded metadata: 397296 docs, 666727 terms, avg_length=130.68
2025-07-24 15:18:37,110 - __main__ - INFO - LMDB BM25 index ready: 666727 terms, 397296 documents
2025-07-24 15:18:37,111 - __main__ - INFO - Cache loaded from disk in 0.30s
2025-07-24 15:18:37,112 - __main__ - INFO - Loaded 397296 records with memory-mapped storage
2025-07-24 15:18:37,767 - __main__ - INFO - Search cache loaded: 397296 records
2025-07-24 15:18:37,768 - __main__ - INFO - Memory-mapped storage: True
2025-07-24 15:18:37,768 - __main__ - INFO - Starting system monitor...
2025-07-24 15:18:37,770 - __main__ - INFO - 
System monitor started

2025-07-24 15:18:37,770 - __main__ - INFO - Starting cache update monitor...
2025-07-24 15:18:37,771 - __main__ - INFO - Cache update monitor started
2025-07-24 15:18:37,772 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 300s
2025-07-24 15:18:37,793 - __main__ - INFO - No new records found, incremental update not needed
2025-07-24 15:23:37,790 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-01 15:33:13.990000. Check Interval: 300s
2025-07-24 15:23:37,906 - __main__ - INFO - Starting incremental update for 1198 new records
2025-07-24 15:23:38,033 - __main__ - INFO - Loading new records since 2016-12-01 15:33:13.990000 with chunk size 5000
2025-07-24 15:23:38,172 - __main__ - INFO - Loading 1198 new records incrementally
2025-07-24 15:23:38,218 - __main__ - INFO - Processing incremental chunk 1: records 0-1198
2025-07-24 15:23:43,207 - __main__ - INFO - Incremental progress: 1198/1198 records processed (100.0%)
2025-07-24 15:23:43,238 - __main__ - INFO - Incremental loading complete: 1198 new records loaded
2025-07-24 15:23:43,341 - __main__ - INFO - Starting incremental cache update with 1198 new records
2025-07-24 15:23:43,566 - __main__ - INFO - Extending embeddings from 397296 to 398494 records
2025-07-24 15:23:44,187 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 15:23:50,828 - __main__ - INFO - Embeddings file extended successfully to 398494 records
2025-07-24 15:23:50,907 - __main__ - INFO - Adding 1198 documents to LMDB BM25 index incrementally...
2025-07-24 15:26:51,447 - __main__ - INFO - Incremental update complete: 398494 total docs, 668579 terms
2025-07-24 15:26:52,076 - __main__ - INFO - Incremental cache update completed in 188.74s
2025-07-24 15:26:52,077 - __main__ - INFO - Cache now contains 398494 total records
2025-07-24 15:26:52,711 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 1198, 'total_records': 398494, 'update_time_seconds': 188.73552680015564}
2025-07-24 15:26:52,716 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 1198, 'total_records': 398494, 'update_time_seconds': 188.73552680015564, 'latest_entered_time': '2016-12-01 15:33:13.990000', 'memory_used_mb': 1.7548828125}
2025-07-24 15:31:52,714 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2016-12-08 16:30:41.680000. Check Interval: 300s
2025-07-24 15:31:52,887 - __main__ - INFO - Starting incremental update for 5997 new records
2025-07-24 15:31:53,012 - __main__ - INFO - Loading new records since 2016-12-08 16:30:41.680000 with chunk size 5000
2025-07-24 15:31:53,190 - __main__ - INFO - Loading 5997 new records incrementally
2025-07-24 15:31:53,217 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 15:32:10,106 - __main__ - INFO - Incremental progress: 5000/5997 records processed (83.4%)
2025-07-24 15:32:10,179 - __main__ - INFO - Processing incremental chunk 2: records 5000-5997
2025-07-24 15:32:14,296 - __main__ - INFO - Incremental progress: 5997/5997 records processed (100.0%)
2025-07-24 15:32:14,297 - __main__ - INFO - Incremental loading complete: 5997 new records loaded
2025-07-24 15:32:14,300 - __main__ - INFO - Starting incremental cache update with 5997 new records
2025-07-24 15:32:14,556 - __main__ - INFO - Extending embeddings from 398494 to 404491 records
2025-07-24 15:32:15,033 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 15:32:23,180 - __main__ - INFO - Embeddings file extended successfully to 404491 records
2025-07-24 15:32:23,299 - __main__ - INFO - Adding 5997 documents to LMDB BM25 index incrementally...
2025-07-24 15:35:42,534 - __main__ - INFO - Incremental update complete: 404491 total docs, 678004 terms
2025-07-24 15:35:43,567 - __main__ - INFO - Incremental cache update completed in 209.27s
2025-07-24 15:35:43,571 - __main__ - INFO - Cache now contains 404491 total records
2025-07-24 15:35:44,404 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 5997, 'total_records': 404491, 'update_time_seconds': 209.26600122451782}
2025-07-24 15:35:44,415 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 5997, 'total_records': 404491, 'update_time_seconds': 209.26600122451782, 'latest_entered_time': '2016-12-08 16:30:41.680000', 'memory_used_mb': 8.78466796875}
2025-07-24 15:40:44,416 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-01-15 11:54:35.967000. Check Interval: 300s
2025-07-24 15:40:44,499 - __main__ - INFO - Starting incremental update for 6000 new records
2025-07-24 15:40:44,512 - __main__ - INFO - Loading new records since 2017-01-15 11:54:35.967000 with chunk size 5000
2025-07-24 15:40:44,582 - __main__ - INFO - Loading 6000 new records incrementally
2025-07-24 15:40:44,612 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 15:40:59,829 - __main__ - INFO - Incremental progress: 5000/6000 records processed (83.3%)
2025-07-24 15:40:59,875 - __main__ - INFO - Processing incremental chunk 2: records 5000-6000
2025-07-24 15:41:05,406 - __main__ - INFO - Incremental progress: 6300/6000 records processed (105.0%)
2025-07-24 15:41:05,481 - __main__ - INFO - Incremental loading complete: 6300 new records loaded
2025-07-24 15:41:05,501 - __main__ - INFO - Starting incremental cache update with 6300 new records
2025-07-24 15:41:05,882 - __main__ - INFO - Extending embeddings from 404491 to 410791 records
2025-07-24 15:41:06,410 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 15:41:14,936 - __main__ - INFO - Embeddings file extended successfully to 410791 records
2025-07-24 15:41:14,937 - __main__ - INFO - Adding 6300 documents to LMDB BM25 index incrementally...
2025-07-24 15:44:38,268 - __main__ - INFO - Incremental update complete: 410791 total docs, 688252 terms
2025-07-24 15:44:39,552 - __main__ - INFO - Incremental cache update completed in 214.05s
2025-07-24 15:44:39,569 - __main__ - INFO - Cache now contains 410791 total records
2025-07-24 15:44:40,997 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6300, 'total_records': 410791, 'update_time_seconds': 214.05126428604126}
2025-07-24 15:44:41,036 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6300, 'total_records': 410791, 'update_time_seconds': 214.05126428604126, 'latest_entered_time': '2017-01-15 11:54:35.967000', 'memory_used_mb': 9.228515625}
2025-07-24 15:49:41,148 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-02-20 17:18:04.070000. Check Interval: 300s
2025-07-24 15:49:42,268 - __main__ - INFO - Starting incremental update for 6298 new records
2025-07-24 15:49:42,303 - __main__ - INFO - Loading new records since 2017-02-20 17:18:04.070000 with chunk size 5000
2025-07-24 15:49:42,370 - __main__ - INFO - Loading 6298 new records incrementally
2025-07-24 15:49:42,490 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 15:49:57,814 - __main__ - INFO - Incremental progress: 5000/6298 records processed (79.4%)
2025-07-24 15:49:57,844 - __main__ - INFO - Processing incremental chunk 2: records 5000-6298
2025-07-24 15:50:03,484 - __main__ - INFO - Incremental progress: 6598/6298 records processed (104.8%)
2025-07-24 15:50:03,495 - __main__ - INFO - Incremental loading complete: 6598 new records loaded
2025-07-24 15:50:03,518 - __main__ - INFO - Starting incremental cache update with 6598 new records
2025-07-24 15:50:03,878 - __main__ - INFO - Extending embeddings from 410791 to 417389 records
2025-07-24 15:50:04,812 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 15:50:20,503 - __main__ - INFO - Embeddings file extended successfully to 417389 records
2025-07-24 15:50:20,504 - __main__ - INFO - Adding 6598 documents to LMDB BM25 index incrementally...
2025-07-24 15:53:38,575 - __main__ - INFO - Incremental update complete: 417389 total docs, 698864 terms
2025-07-24 15:53:39,873 - __main__ - INFO - Incremental cache update completed in 216.36s
2025-07-24 15:53:40,027 - __main__ - INFO - Cache now contains 417389 total records
2025-07-24 15:53:41,343 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6598, 'total_records': 417389, 'update_time_seconds': 216.3554391860962}
2025-07-24 15:53:41,364 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6598, 'total_records': 417389, 'update_time_seconds': 216.3554391860962, 'latest_entered_time': '2017-02-20 17:18:04.070000', 'memory_used_mb': 9.6650390625}
2025-07-24 15:58:41,465 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-03-29 10:34:15.340000. Check Interval: 300s
2025-07-24 15:58:41,619 - __main__ - INFO - Starting incremental update for 6300 new records
2025-07-24 15:58:41,728 - __main__ - INFO - Loading new records since 2017-03-29 10:34:15.340000 with chunk size 5000
2025-07-24 15:58:41,839 - __main__ - INFO - Loading 6300 new records incrementally
2025-07-24 15:58:41,895 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 15:58:58,862 - __main__ - INFO - Incremental progress: 5000/6300 records processed (79.4%)
2025-07-24 15:58:58,863 - __main__ - INFO - Processing incremental chunk 2: records 5000-6300
2025-07-24 15:59:02,073 - __main__ - INFO - Incremental progress: 6300/6300 records processed (100.0%)
2025-07-24 15:59:02,355 - __main__ - INFO - Incremental loading complete: 6300 new records loaded
2025-07-24 15:59:02,524 - __main__ - INFO - Starting incremental cache update with 6300 new records
2025-07-24 15:59:03,199 - __main__ - INFO - Extending embeddings from 417389 to 423689 records
2025-07-24 15:59:04,147 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 15:59:17,985 - __main__ - INFO - Embeddings file extended successfully to 423689 records
2025-07-24 15:59:18,064 - __main__ - INFO - Adding 6300 documents to LMDB BM25 index incrementally...
2025-07-24 16:02:30,701 - __main__ - INFO - Incremental update complete: 423689 total docs, 709882 terms
2025-07-24 16:02:32,444 - __main__ - INFO - Incremental cache update completed in 209.92s
2025-07-24 16:02:32,461 - __main__ - INFO - Cache now contains 423689 total records
2025-07-24 16:02:35,157 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6300, 'total_records': 423689, 'update_time_seconds': 209.91988825798035}
2025-07-24 16:02:35,190 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6300, 'total_records': 423689, 'update_time_seconds': 209.91988825798035, 'latest_entered_time': '2017-03-29 10:34:15.340000', 'memory_used_mb': 9.228515625}
2025-07-24 16:07:35,226 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-05-02 01:19:35.133000. Check Interval: 300s
2025-07-24 16:07:35,369 - __main__ - INFO - Starting incremental update for 6899 new records
2025-07-24 16:07:35,371 - __main__ - INFO - Loading new records since 2017-05-02 01:19:35.133000 with chunk size 5000
2025-07-24 16:07:35,450 - __main__ - INFO - Loading 6899 new records incrementally
2025-07-24 16:07:35,451 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:07:53,450 - __main__ - INFO - Incremental progress: 5000/6899 records processed (72.5%)
2025-07-24 16:07:53,459 - __main__ - INFO - Processing incremental chunk 2: records 5000-6899
2025-07-24 16:07:58,890 - __main__ - INFO - Incremental progress: 6899/6899 records processed (100.0%)
2025-07-24 16:07:58,891 - __main__ - INFO - Incremental loading complete: 6899 new records loaded
2025-07-24 16:07:58,896 - __main__ - INFO - Starting incremental cache update with 6899 new records
2025-07-24 16:07:58,997 - __main__ - INFO - Extending embeddings from 423689 to 430588 records
2025-07-24 16:07:59,225 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:08:13,641 - __main__ - INFO - Embeddings file extended successfully to 430588 records
2025-07-24 16:08:13,651 - __main__ - INFO - Adding 6899 documents to LMDB BM25 index incrementally...
2025-07-24 16:11:38,905 - __main__ - INFO - Incremental update complete: 430588 total docs, 721570 terms
2025-07-24 16:11:40,419 - __main__ - INFO - Incremental cache update completed in 221.52s
2025-07-24 16:11:40,421 - __main__ - INFO - Cache now contains 430588 total records
2025-07-24 16:11:43,218 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6899, 'total_records': 430588, 'update_time_seconds': 221.52370166778564}
2025-07-24 16:11:43,308 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6899, 'total_records': 430588, 'update_time_seconds': 221.52370166778564, 'latest_entered_time': '2017-05-02 01:19:35.133000', 'memory_used_mb': 10.10595703125}
2025-07-24 16:16:43,425 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-06-07 01:14:28.213000. Check Interval: 300s
2025-07-24 16:16:43,705 - __main__ - INFO - Starting incremental update for 6599 new records
2025-07-24 16:16:43,741 - __main__ - INFO - Loading new records since 2017-06-07 01:14:28.213000 with chunk size 5000
2025-07-24 16:16:43,830 - __main__ - INFO - Loading 6599 new records incrementally
2025-07-24 16:16:43,839 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:16:59,640 - __main__ - INFO - Incremental progress: 5000/6599 records processed (75.8%)
2025-07-24 16:16:59,641 - __main__ - INFO - Processing incremental chunk 2: records 5000-6599
2025-07-24 16:17:07,359 - __main__ - INFO - Incremental progress: 6899/6599 records processed (104.5%)
2025-07-24 16:17:07,373 - __main__ - INFO - Incremental loading complete: 6899 new records loaded
2025-07-24 16:17:07,382 - __main__ - INFO - Starting incremental cache update with 6899 new records
2025-07-24 16:17:07,771 - __main__ - INFO - Extending embeddings from 430588 to 437487 records
2025-07-24 16:17:08,324 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:17:26,473 - __main__ - INFO - Embeddings file extended successfully to 437487 records
2025-07-24 16:17:26,501 - __main__ - INFO - Adding 6899 documents to LMDB BM25 index incrementally...
2025-07-24 16:20:47,218 - __main__ - INFO - Incremental update complete: 437487 total docs, 732539 terms
2025-07-24 16:20:48,782 - __main__ - INFO - Incremental cache update completed in 221.40s
2025-07-24 16:20:48,903 - __main__ - INFO - Cache now contains 437487 total records
2025-07-24 16:20:51,846 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6899, 'total_records': 437487, 'update_time_seconds': 221.3995349407196}
2025-07-24 16:20:51,870 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6899, 'total_records': 437487, 'update_time_seconds': 221.3995349407196, 'latest_entered_time': '2017-06-07 01:14:28.213000', 'memory_used_mb': 10.10595703125}
2025-07-24 16:25:51,871 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-07-10 14:03:01.380000. Check Interval: 300s
2025-07-24 16:25:52,934 - __main__ - INFO - Starting incremental update for 6297 new records
2025-07-24 16:25:53,013 - __main__ - INFO - Loading new records since 2017-07-10 14:03:01.380000 with chunk size 5000
2025-07-24 16:25:53,091 - __main__ - INFO - Loading 6297 new records incrementally
2025-07-24 16:25:53,127 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:26:08,904 - __main__ - INFO - Incremental progress: 5000/6297 records processed (79.4%)
2025-07-24 16:26:08,915 - __main__ - INFO - Processing incremental chunk 2: records 5000-6297
2025-07-24 16:26:15,384 - __main__ - INFO - Incremental progress: 6597/6297 records processed (104.8%)
2025-07-24 16:26:15,399 - __main__ - INFO - Incremental loading complete: 6597 new records loaded
2025-07-24 16:26:15,405 - __main__ - INFO - Starting incremental cache update with 6597 new records
2025-07-24 16:26:15,845 - __main__ - INFO - Extending embeddings from 437487 to 444084 records
2025-07-24 16:26:16,394 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:26:22,156 - __main__ - INFO - Embeddings file extended successfully to 444084 records
2025-07-24 16:26:22,233 - __main__ - INFO - Adding 6597 documents to LMDB BM25 index incrementally...
2025-07-24 16:29:51,780 - __main__ - INFO - Incremental update complete: 444084 total docs, 742777 terms
2025-07-24 16:29:53,034 - __main__ - INFO - Incremental cache update completed in 217.63s
2025-07-24 16:29:53,108 - __main__ - INFO - Cache now contains 444084 total records
2025-07-24 16:29:54,379 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6597, 'total_records': 444084, 'update_time_seconds': 217.62890100479126}
2025-07-24 16:29:54,402 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6597, 'total_records': 444084, 'update_time_seconds': 217.62890100479126, 'latest_entered_time': '2017-07-10 14:03:01.380000', 'memory_used_mb': 9.66357421875}
2025-07-24 16:34:54,413 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-08-13 16:00:48.723000. Check Interval: 300s
2025-07-24 16:34:54,521 - __main__ - INFO - Starting incremental update for 6598 new records
2025-07-24 16:34:54,582 - __main__ - INFO - Loading new records since 2017-08-13 16:00:48.723000 with chunk size 5000
2025-07-24 16:34:54,697 - __main__ - INFO - Loading 6598 new records incrementally
2025-07-24 16:34:54,737 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:35:11,167 - __main__ - INFO - Incremental progress: 5000/6598 records processed (75.8%)
2025-07-24 16:35:11,170 - __main__ - INFO - Processing incremental chunk 2: records 5000-6598
2025-07-24 16:35:16,258 - __main__ - INFO - Incremental progress: 6598/6598 records processed (100.0%)
2025-07-24 16:35:16,259 - __main__ - INFO - Incremental loading complete: 6598 new records loaded
2025-07-24 16:35:16,265 - __main__ - INFO - Starting incremental cache update with 6598 new records
2025-07-24 16:35:16,398 - __main__ - INFO - Extending embeddings from 444084 to 450682 records
2025-07-24 16:35:16,683 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:35:27,352 - __main__ - INFO - Embeddings file extended successfully to 450682 records
2025-07-24 16:35:27,486 - __main__ - INFO - Adding 6598 documents to LMDB BM25 index incrementally...
2025-07-24 16:38:24,295 - __main__ - INFO - Incremental update complete: 450682 total docs, 753445 terms
2025-07-24 16:38:25,956 - __main__ - INFO - Incremental cache update completed in 189.69s
2025-07-24 16:38:26,060 - __main__ - INFO - Cache now contains 450682 total records
2025-07-24 16:38:27,265 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6598, 'total_records': 450682, 'update_time_seconds': 189.69070506095886}
2025-07-24 16:38:27,318 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6598, 'total_records': 450682, 'update_time_seconds': 189.69070506095886, 'latest_entered_time': '2017-08-13 16:00:48.723000', 'memory_used_mb': 9.6650390625}
2025-07-24 16:43:27,447 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-09-19 03:24:35.830000. Check Interval: 300s
2025-07-24 16:43:27,559 - __main__ - INFO - Starting incremental update for 5999 new records
2025-07-24 16:43:27,561 - __main__ - INFO - Loading new records since 2017-09-19 03:24:35.830000 with chunk size 5000
2025-07-24 16:43:27,624 - __main__ - INFO - Loading 5999 new records incrementally
2025-07-24 16:43:27,632 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:43:42,137 - __main__ - INFO - Incremental progress: 5000/5999 records processed (83.3%)
2025-07-24 16:43:42,193 - __main__ - INFO - Processing incremental chunk 2: records 5000-5999
2025-07-24 16:43:46,658 - __main__ - INFO - Incremental progress: 6299/5999 records processed (105.0%)
2025-07-24 16:43:46,827 - __main__ - INFO - Incremental loading complete: 6299 new records loaded
2025-07-24 16:43:46,839 - __main__ - INFO - Starting incremental cache update with 6299 new records
2025-07-24 16:43:47,414 - __main__ - INFO - Extending embeddings from 450682 to 456981 records
2025-07-24 16:43:48,206 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:44:01,942 - __main__ - INFO - Embeddings file extended successfully to 456981 records
2025-07-24 16:44:01,943 - __main__ - INFO - Adding 6299 documents to LMDB BM25 index incrementally...
2025-07-24 16:46:56,916 - __main__ - INFO - Incremental update complete: 456981 total docs, 763364 terms
2025-07-24 16:46:58,352 - __main__ - INFO - Incremental cache update completed in 191.51s
2025-07-24 16:46:58,461 - __main__ - INFO - Cache now contains 456981 total records
2025-07-24 16:47:00,011 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6299, 'total_records': 456981, 'update_time_seconds': 191.5131766796112}
2025-07-24 16:47:00,025 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6299, 'total_records': 456981, 'update_time_seconds': 191.5131766796112, 'latest_entered_time': '2017-09-19 03:24:35.830000', 'memory_used_mb': 9.22705078125}
2025-07-24 16:52:00,038 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-10-19 19:26:56.757000. Check Interval: 300s
2025-07-24 16:52:00,228 - __main__ - INFO - Starting incremental update for 5999 new records
2025-07-24 16:52:00,229 - __main__ - INFO - Loading new records since 2017-10-19 19:26:56.757000 with chunk size 5000
2025-07-24 16:52:00,292 - __main__ - INFO - Loading 5999 new records incrementally
2025-07-24 16:52:00,304 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 16:52:13,304 - __main__ - INFO - Incremental progress: 5000/5999 records processed (83.3%)
2025-07-24 16:52:13,332 - __main__ - INFO - Processing incremental chunk 2: records 5000-5999
2025-07-24 16:52:19,415 - __main__ - INFO - Incremental progress: 6298/5999 records processed (105.0%)
2025-07-24 16:52:19,456 - __main__ - INFO - Incremental loading complete: 6298 new records loaded
2025-07-24 16:52:19,466 - __main__ - INFO - Starting incremental cache update with 6298 new records
2025-07-24 16:52:20,048 - __main__ - INFO - Extending embeddings from 456981 to 463279 records
2025-07-24 16:52:20,630 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 16:52:28,358 - __main__ - INFO - Embeddings file extended successfully to 463279 records
2025-07-24 16:52:28,359 - __main__ - INFO - Adding 6298 documents to LMDB BM25 index incrementally...
2025-07-24 16:55:34,415 - __main__ - INFO - Incremental update complete: 463279 total docs, 772939 terms
2025-07-24 16:55:35,827 - __main__ - INFO - Incremental cache update completed in 196.36s
2025-07-24 16:55:35,935 - __main__ - INFO - Cache now contains 463279 total records
2025-07-24 16:55:37,340 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6298, 'total_records': 463279, 'update_time_seconds': 196.3605399131775}
2025-07-24 16:55:37,374 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6298, 'total_records': 463279, 'update_time_seconds': 196.3605399131775, 'latest_entered_time': '2017-10-19 19:26:56.757000', 'memory_used_mb': 9.2255859375}
2025-07-24 17:00:37,384 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-11-19 16:07:15.587000. Check Interval: 300s
2025-07-24 17:00:38,609 - __main__ - INFO - Starting incremental update for 5399 new records
2025-07-24 17:00:38,633 - __main__ - INFO - Loading new records since 2017-11-19 16:07:15.587000 with chunk size 5000
2025-07-24 17:00:38,699 - __main__ - INFO - Loading 5399 new records incrementally
2025-07-24 17:00:38,739 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:00:52,986 - __main__ - INFO - Incremental progress: 5000/5399 records processed (92.6%)
2025-07-24 17:00:53,012 - __main__ - INFO - Processing incremental chunk 2: records 5000-5399
2025-07-24 17:00:56,673 - __main__ - INFO - Incremental progress: 5699/5399 records processed (105.6%)
2025-07-24 17:00:56,689 - __main__ - INFO - Incremental loading complete: 5699 new records loaded
2025-07-24 17:00:56,752 - __main__ - INFO - Starting incremental cache update with 5699 new records
2025-07-24 17:00:57,138 - __main__ - INFO - Extending embeddings from 463279 to 468978 records
2025-07-24 17:00:57,921 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:01:12,002 - __main__ - INFO - Embeddings file extended successfully to 468978 records
2025-07-24 17:01:12,016 - __main__ - INFO - Adding 5699 documents to LMDB BM25 index incrementally...
2025-07-24 17:04:51,589 - __main__ - INFO - Incremental update complete: 468978 total docs, 782422 terms
2025-07-24 17:04:52,825 - __main__ - INFO - Incremental cache update completed in 236.07s
2025-07-24 17:04:52,875 - __main__ - INFO - Cache now contains 468978 total records
2025-07-24 17:04:54,232 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 5699, 'total_records': 468978, 'update_time_seconds': 236.0729956626892}
2025-07-24 17:04:54,334 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 5699, 'total_records': 468978, 'update_time_seconds': 236.0729956626892, 'latest_entered_time': '2017-11-19 16:07:15.587000', 'memory_used_mb': 8.34814453125}
2025-07-24 17:09:54,381 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2017-12-17 04:31:36.977000. Check Interval: 300s
2025-07-24 17:09:54,551 - __main__ - INFO - Starting incremental update for 6599 new records
2025-07-24 17:09:54,581 - __main__ - INFO - Loading new records since 2017-12-17 04:31:36.977000 with chunk size 5000
2025-07-24 17:09:54,662 - __main__ - INFO - Loading 6599 new records incrementally
2025-07-24 17:09:54,709 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:10:12,016 - __main__ - INFO - Incremental progress: 5000/6599 records processed (75.8%)
2025-07-24 17:10:12,017 - __main__ - INFO - Processing incremental chunk 2: records 5000-6599
2025-07-24 17:10:16,950 - __main__ - INFO - Incremental progress: 6599/6599 records processed (100.0%)
2025-07-24 17:10:16,951 - __main__ - INFO - Incremental loading complete: 6599 new records loaded
2025-07-24 17:10:16,957 - __main__ - INFO - Starting incremental cache update with 6599 new records
2025-07-24 17:10:17,092 - __main__ - INFO - Extending embeddings from 468978 to 475577 records
2025-07-24 17:10:17,375 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:10:30,387 - __main__ - INFO - Embeddings file extended successfully to 475577 records
2025-07-24 17:10:30,463 - __main__ - INFO - Adding 6599 documents to LMDB BM25 index incrementally...
2025-07-24 17:14:00,899 - __main__ - INFO - Incremental update complete: 475577 total docs, 791647 terms
2025-07-24 17:14:02,447 - __main__ - INFO - Incremental cache update completed in 225.49s
2025-07-24 17:14:02,454 - __main__ - INFO - Cache now contains 475577 total records
2025-07-24 17:14:03,691 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6599, 'total_records': 475577, 'update_time_seconds': 225.48981022834778}
2025-07-24 17:14:03,734 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6599, 'total_records': 475577, 'update_time_seconds': 225.48981022834778, 'latest_entered_time': '2017-12-17 04:31:36.977000', 'memory_used_mb': 9.66650390625}
2025-07-24 17:19:03,743 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-01-20 23:29:56.363000. Check Interval: 300s
2025-07-24 17:19:03,812 - __main__ - INFO - Starting incremental update for 6299 new records
2025-07-24 17:19:03,861 - __main__ - INFO - Loading new records since 2018-01-20 23:29:56.363000 with chunk size 5000
2025-07-24 17:19:03,930 - __main__ - INFO - Loading 6299 new records incrementally
2025-07-24 17:19:03,959 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:19:18,024 - __main__ - INFO - Incremental progress: 5000/6299 records processed (79.4%)
2025-07-24 17:19:18,028 - __main__ - INFO - Processing incremental chunk 2: records 5000-6299
2025-07-24 17:19:23,256 - __main__ - INFO - Incremental progress: 6599/6299 records processed (104.8%)
2025-07-24 17:19:23,260 - __main__ - INFO - Incremental loading complete: 6599 new records loaded
2025-07-24 17:19:23,268 - __main__ - INFO - Starting incremental cache update with 6599 new records
2025-07-24 17:19:23,475 - __main__ - INFO - Extending embeddings from 475577 to 482176 records
2025-07-24 17:19:24,079 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:19:32,542 - __main__ - INFO - Embeddings file extended successfully to 482176 records
2025-07-24 17:19:32,545 - __main__ - INFO - Adding 6599 documents to LMDB BM25 index incrementally...
2025-07-24 17:22:32,522 - __main__ - INFO - Incremental update complete: 482176 total docs, 801186 terms
2025-07-24 17:22:33,971 - __main__ - INFO - Incremental cache update completed in 190.70s
2025-07-24 17:22:33,988 - __main__ - INFO - Cache now contains 482176 total records
2025-07-24 17:22:35,177 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6599, 'total_records': 482176, 'update_time_seconds': 190.7031171321869}
2025-07-24 17:22:35,193 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6599, 'total_records': 482176, 'update_time_seconds': 190.7031171321869, 'latest_entered_time': '2018-01-20 23:29:56.363000', 'memory_used_mb': 9.66650390625}
2025-07-24 17:27:35,203 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-02-23 16:16:57.057000. Check Interval: 300s
2025-07-24 17:27:35,348 - __main__ - INFO - Starting incremental update for 5999 new records
2025-07-24 17:27:35,371 - __main__ - INFO - Loading new records since 2018-02-23 16:16:57.057000 with chunk size 5000
2025-07-24 17:27:35,480 - __main__ - INFO - Loading 5999 new records incrementally
2025-07-24 17:27:35,498 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:27:48,528 - __main__ - INFO - Incremental progress: 5000/5999 records processed (83.3%)
2025-07-24 17:27:48,685 - __main__ - INFO - Processing incremental chunk 2: records 5000-5999
2025-07-24 17:27:54,444 - __main__ - INFO - Incremental progress: 6298/5999 records processed (105.0%)
2025-07-24 17:27:54,514 - __main__ - INFO - Incremental loading complete: 6298 new records loaded
2025-07-24 17:27:54,525 - __main__ - INFO - Starting incremental cache update with 6298 new records
2025-07-24 17:27:54,761 - __main__ - INFO - Extending embeddings from 482176 to 488474 records
2025-07-24 17:27:55,352 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:28:04,421 - __main__ - INFO - Embeddings file extended successfully to 488474 records
2025-07-24 17:28:04,422 - __main__ - INFO - Adding 6298 documents to LMDB BM25 index incrementally...
2025-07-24 17:31:19,192 - __main__ - INFO - Incremental update complete: 488474 total docs, 810444 terms
2025-07-24 17:31:19,764 - __main__ - INFO - Incremental cache update completed in 205.24s
2025-07-24 17:31:19,765 - __main__ - INFO - Cache now contains 488474 total records
2025-07-24 17:31:20,252 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6298, 'total_records': 488474, 'update_time_seconds': 205.23894238471985}
2025-07-24 17:31:20,262 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6298, 'total_records': 488474, 'update_time_seconds': 205.23894238471985, 'latest_entered_time': '2018-02-23 16:16:57.057000', 'memory_used_mb': 9.2255859375}
2025-07-24 17:36:20,292 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-03-23 17:56:00.247000. Check Interval: 300s
2025-07-24 17:36:21,532 - __main__ - INFO - Starting incremental update for 6300 new records
2025-07-24 17:36:21,540 - __main__ - INFO - Loading new records since 2018-03-23 17:56:00.247000 with chunk size 5000
2025-07-24 17:36:21,612 - __main__ - INFO - Loading 6300 new records incrementally
2025-07-24 17:36:21,613 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:36:34,434 - __main__ - INFO - Incremental progress: 5000/6300 records processed (79.4%)
2025-07-24 17:36:34,443 - __main__ - INFO - Processing incremental chunk 2: records 5000-6300
2025-07-24 17:36:40,659 - __main__ - INFO - Incremental progress: 6600/6300 records processed (104.8%)
2025-07-24 17:36:40,671 - __main__ - INFO - Incremental loading complete: 6600 new records loaded
2025-07-24 17:36:40,674 - __main__ - INFO - Starting incremental cache update with 6600 new records
2025-07-24 17:36:40,980 - __main__ - INFO - Extending embeddings from 488474 to 495074 records
2025-07-24 17:36:41,585 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:36:52,102 - __main__ - INFO - Embeddings file extended successfully to 495074 records
2025-07-24 17:36:52,103 - __main__ - INFO - Adding 6600 documents to LMDB BM25 index incrementally...
2025-07-24 17:40:48,870 - __main__ - INFO - Incremental update complete: 495074 total docs, 819032 terms
2025-07-24 17:40:50,442 - __main__ - INFO - Incremental cache update completed in 249.77s
2025-07-24 17:40:50,453 - __main__ - INFO - Cache now contains 495074 total records
2025-07-24 17:40:51,415 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6600, 'total_records': 495074, 'update_time_seconds': 249.7679340839386}
2025-07-24 17:40:51,431 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6600, 'total_records': 495074, 'update_time_seconds': 249.7679340839386, 'latest_entered_time': '2018-03-23 17:56:00.247000', 'memory_used_mb': 9.66796875}
2025-07-24 17:45:51,431 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-04-23 11:25:18.207000. Check Interval: 300s
2025-07-24 17:45:51,516 - __main__ - INFO - Starting incremental update for 6899 new records
2025-07-24 17:45:51,517 - __main__ - INFO - Loading new records since 2018-04-23 11:25:18.207000 with chunk size 5000
2025-07-24 17:45:51,599 - __main__ - INFO - Loading 6899 new records incrementally
2025-07-24 17:45:51,599 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:46:07,175 - __main__ - INFO - Incremental progress: 5000/6899 records processed (72.5%)
2025-07-24 17:46:07,196 - __main__ - INFO - Processing incremental chunk 2: records 5000-6899
2025-07-24 17:46:13,836 - __main__ - INFO - Incremental progress: 6899/6899 records processed (100.0%)
2025-07-24 17:46:13,839 - __main__ - INFO - Incremental loading complete: 6899 new records loaded
2025-07-24 17:46:13,846 - __main__ - INFO - Starting incremental cache update with 6899 new records
2025-07-24 17:46:14,005 - __main__ - INFO - Extending embeddings from 495074 to 501973 records
2025-07-24 17:46:14,283 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:46:24,259 - __main__ - INFO - Embeddings file extended successfully to 501973 records
2025-07-24 17:46:24,335 - __main__ - INFO - Adding 6899 documents to LMDB BM25 index incrementally...
2025-07-24 17:50:04,708 - __main__ - INFO - Incremental update complete: 501973 total docs, 828542 terms
2025-07-24 17:50:06,258 - __main__ - INFO - Incremental cache update completed in 232.41s
2025-07-24 17:50:06,325 - __main__ - INFO - Cache now contains 501973 total records
2025-07-24 17:50:07,486 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 6899, 'total_records': 501973, 'update_time_seconds': 232.41109442710876}
2025-07-24 17:50:07,512 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 6899, 'total_records': 501973, 'update_time_seconds': 232.41109442710876, 'latest_entered_time': '2018-04-23 11:25:18.207000', 'memory_used_mb': 10.10595703125}
2025-07-24 17:55:07,521 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-05-25 09:06:04.583000. Check Interval: 300s
2025-07-24 17:55:07,758 - __main__ - INFO - Starting incremental update for 6899 new records
2025-07-24 17:55:07,787 - __main__ - INFO - Loading new records since 2018-05-25 09:06:04.583000 with chunk size 5000
2025-07-24 17:55:07,858 - __main__ - INFO - Loading 6899 new records incrementally
2025-07-24 17:55:07,979 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 17:55:22,200 - __main__ - INFO - Incremental progress: 5000/6899 records processed (72.5%)
2025-07-24 17:55:22,201 - __main__ - INFO - Processing incremental chunk 2: records 5000-6899
2025-07-24 17:55:30,101 - __main__ - INFO - Incremental progress: 7199/6899 records processed (104.3%)
2025-07-24 17:55:30,404 - __main__ - INFO - Incremental loading complete: 7199 new records loaded
2025-07-24 17:55:30,466 - __main__ - INFO - Starting incremental cache update with 7199 new records
2025-07-24 17:55:30,781 - __main__ - INFO - Extending embeddings from 501973 to 509172 records
2025-07-24 17:55:31,277 - __main__ - INFO - Temporarily closed existing embeddings memory map to avoid file locking
2025-07-24 17:55:41,403 - __main__ - INFO - Embeddings file extended successfully to 509172 records
2025-07-24 17:55:41,417 - __main__ - INFO - Adding 7199 documents to LMDB BM25 index incrementally...
2025-07-24 17:58:47,812 - __main__ - INFO - Incremental update complete: 509172 total docs, 838564 terms
2025-07-24 17:58:49,463 - __main__ - INFO - Incremental cache update completed in 199.00s
2025-07-24 17:58:49,464 - __main__ - INFO - Cache now contains 509172 total records
2025-07-24 17:58:50,608 - __main__ - INFO - Incremental cache update completed successfully: {'success': True, 'new_records_added': 7199, 'total_records': 509172, 'update_time_seconds': 198.99715948104858}
2025-07-24 17:58:50,639 - __main__ - INFO - Automatic cache update completed: {'success': True, 'new_records_added': 7199, 'total_records': 509172, 'update_time_seconds': 198.99715948104858, 'latest_entered_time': '2018-05-25 09:06:04.583000', 'memory_used_mb': 10.54541015625}
2025-07-24 18:03:50,686 - __main__ - INFO - Running incremental update using cached latest Entered_Time: 2018-06-26 02:38:35.280000. Check Interval: 300s
2025-07-24 18:03:50,888 - __main__ - INFO - Starting incremental update for 6300 new records
2025-07-24 18:03:50,944 - __main__ - INFO - Loading new records since 2018-06-26 02:38:35.280000 with chunk size 5000
2025-07-24 18:03:51,016 - __main__ - INFO - Loading 6300 new records incrementally
2025-07-24 18:03:51,155 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-24 18:04:06,790 - __main__ - INFO - Incremental progress: 5000/6300 records processed (79.4%)
2025-07-24 18:04:06,791 - __main__ - INFO - Processing incremental chunk 2: records 5000-6300
