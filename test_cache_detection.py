#!/usr/bin/env python3
"""
Test script to verify cache detection works correctly with LMDB files.
"""

import os
import sys
import tempfile
import shutil

# Add the current directory to path to import from backend_api
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the necessary components
from backend_api import LMDBBackedBM25, check_cache_files_exist

def test_cache_detection():
    """Test that cache detection works with LMDB files."""
    print("Testing cache file detection...")
    
    # Create temporary directory for testing
    test_dir = tempfile.mkdtemp(prefix="test_cache_detection_")
    print(f"Using test directory: {test_dir}")
    
    try:
        # Test 1: Empty directory should return False
        print("\n1. Testing empty directory...")
        result = check_cache_files_exist(test_dir)
        print(f"   Empty directory result: {result}")
        assert result == False, "Empty directory should return False"
        
        # Test 2: Create some files but not all
        print("\n2. Testing partial files...")
        # Create embeddings.dat
        embeddings_file = os.path.join(test_dir, "embeddings.dat")
        with open(embeddings_file, 'wb') as f:
            f.write(b"dummy embeddings data")
        
        result = check_cache_files_exist(test_dir)
        print(f"   Partial files result: {result}")
        assert result == False, "Partial files should return False"
        
        # Test 3: Create all required files
        print("\n3. Testing complete cache...")
        
        # Create ids.pkl
        import pickle
        ids_file = os.path.join(test_dir, "ids.pkl")
        with open(ids_file, 'wb') as f:
            pickle.dump(['doc_1', 'doc_2', 'doc_3'], f)
        
        # Create LMDB BM25 index
        print("   Creating LMDB BM25 index...")
        bm25 = LMDBBackedBM25(cache_dir=test_dir)
        
        # Add some test documents
        test_docs = [
            "The quick brown fox jumps over the lazy dog",
            "A fast brown fox leaps over a sleeping dog", 
            "The dog is lazy and sleeps all day"
        ]
        test_ids = [f"doc_{i}" for i in range(len(test_docs))]
        
        bm25._process_text_chunk(test_ids, test_docs)
        bm25._finalize_build(len(test_docs))
        bm25.close()
        
        # Now test cache detection
        result = check_cache_files_exist(test_dir)
        print(f"   Complete cache result: {result}")
        assert result == True, "Complete cache should return True"
        
        # Test 4: Verify the files that should exist
        print("\n4. Verifying individual files...")
        
        expected_files = [
            "embeddings.dat",
            "ids.pkl", 
            "bm25.lmdb",
            "bm25_metadata.json"
        ]
        
        for filename in expected_files:
            filepath = os.path.join(test_dir, filename)
            exists = os.path.exists(filepath)
            print(f"   {filename}: {'✅' if exists else '❌'}")
            if filename == "bm25.lmdb":
                # LMDB creates a directory
                assert os.path.isdir(filepath), f"{filename} should be a directory"
            else:
                assert exists, f"{filename} should exist"
        
        # Test 5: Test with missing LMDB metadata
        print("\n5. Testing missing metadata...")
        metadata_file = os.path.join(test_dir, "bm25_metadata.json")
        os.remove(metadata_file)
        
        result = check_cache_files_exist(test_dir)
        print(f"   Missing metadata result: {result}")
        assert result == False, "Missing metadata should return False"
        
        print("\n✅ All cache detection tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Cache detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            shutil.rmtree(test_dir)
            print(f"Cleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"Warning: Failed to cleanup {test_dir}: {e}")

def test_cache_structure():
    """Test the structure of LMDB cache files."""
    print("\nTesting LMDB cache structure...")
    
    test_dir = tempfile.mkdtemp(prefix="test_cache_structure_")
    print(f"Using test directory: {test_dir}")
    
    try:
        # Create LMDB BM25 index
        bm25 = LMDBBackedBM25(cache_dir=test_dir)
        
        # Add test documents
        test_docs = [
            "Machine learning algorithms process data efficiently",
            "Deep learning models require substantial computational resources", 
            "Natural language processing enables text understanding",
            "Computer vision systems analyze visual information",
            "Artificial intelligence transforms various industries"
        ]
        test_ids = [f"doc_{i:03d}" for i in range(len(test_docs))]
        
        bm25._process_text_chunk(test_ids, test_docs)
        bm25._finalize_build(len(test_docs))
        
        print(f"Created index: {bm25.doc_count} docs, {bm25.vocab_size} terms")
        
        # Check file structure
        lmdb_path = os.path.join(test_dir, "bm25.lmdb")
        metadata_path = os.path.join(test_dir, "bm25_metadata.json")
        
        print(f"LMDB directory exists: {os.path.exists(lmdb_path)}")
        print(f"Metadata file exists: {os.path.exists(metadata_path)}")
        
        if os.path.exists(lmdb_path):
            lmdb_files = os.listdir(lmdb_path)
            print(f"LMDB files: {lmdb_files}")
        
        # Test loading metadata
        if os.path.exists(metadata_path):
            import json
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            print(f"Metadata keys: {list(metadata.keys())}")
            print(f"Doc count in metadata: {metadata.get('doc_count')}")
            print(f"Vocab size in metadata: {metadata.get('vocab_size')}")
        
        bm25.close()
        
        # Test cache detection
        result = check_cache_files_exist(test_dir)
        print(f"Cache detection result: {result}")
        
        print("\n✅ Cache structure test passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Cache structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        try:
            shutil.rmtree(test_dir)
            print(f"Cleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"Warning: Failed to cleanup {test_dir}: {e}")

if __name__ == "__main__":
    print("LMDB Cache Detection Test")
    print("=" * 50)
    
    try:
        success1 = test_cache_detection()
        success2 = test_cache_structure()
        
        if success1 and success2:
            print("\n🎉 All cache detection tests completed successfully!")
            print("✅ Cache detection is working correctly with LMDB files")
            sys.exit(0)
        else:
            print("\n💥 Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
